import { supabase } from "../src/integrations/supabase/nodeClient";
import { DocumentProcessor } from "../src/lib/rag/documentProcessor";
import { EmbeddingService } from "../src/lib/rag/embeddingService";
import { RAGDocument } from "../src/lib/rag/types";
import { Json } from "../src/integrations/supabase/types";

// Helper function to add delay between requests
function sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
}

async function regenerateEmbeddings() {
    console.log("🔄 Starting embedding regeneration with technology metadata...");

    // 1. Clear existing embeddings
    console.log("🗑️ Clearing existing embeddings...");
    const { error: deleteError } = await supabase
        .from('document_embeddings')
        .delete()
        .neq('id', '00000000-0000-0000-0000-000000000000'); // Delete all records

    if (deleteError) {
        console.error("❌ Error clearing embeddings:", deleteError);
        return;
    }
    console.log("✅ Cleared existing embeddings");

    // 2. Initialize services
    const documentProcessor = new DocumentProcessor();
    const embeddingService = new EmbeddingService();

    // Initialize embedding service with Node.js environment variable
    console.log("🔧 Initializing embedding service...");
    const apiKey = process.env.GEMINI_API_KEY22;
    if (!apiKey) {
        throw new Error("GEMINI_API_KEY not found in environment variables");
    }

    // Manually initialize the embedding service
    const { GoogleGenerativeAI } = await import("@google/generative-ai");
    const genAI = new GoogleGenerativeAI(apiKey);
    (embeddingService as any).model = genAI.getGenerativeModel({ model: "text-embedding-004" });
    (embeddingService as any).isInitialized = true;
    console.log("✅ Embedding service initialized");

    // 3. Load and process documents
    console.log("📚 Loading documents...");
    const documents = await documentProcessor.loadDocuments();
    console.log(`📚 Loaded ${documents.length} documents`);

    // 4. Chunk documents
    console.log("✂️ Chunking documents...");
    const allChunks: RAGDocument[] = [];
    for (const doc of documents) {
        const chunks = documentProcessor.chunkDocument(doc);
        allChunks.push(...chunks);
    }
    console.log(`✂️ Created ${allChunks.length} chunks`);

    // 5. Process chunks in batches of 80
    const batchSize = 80;
    let processedCount = 0;
    let successCount = 0;

    console.log("🔄 Processing chunks with batch embeddings...");
    for (let i = 0; i < allChunks.length; i += batchSize) {
        const batch = allChunks.slice(i, i + batchSize);
        const batchNum = Math.floor(i / batchSize) + 1;
        const totalBatches = Math.ceil(allChunks.length / batchSize);

        console.log(`📦 Processing batch ${batchNum}/${totalBatches} (${batch.length} chunks)...`);

        try {
            // Filter out empty chunks
            const validChunks = batch.filter(chunk => chunk.content && chunk.content.trim().length > 0);

            if (validChunks.length === 0) {
                console.log(`    ⏭️ No valid chunks in batch ${batchNum}`);
                processedCount += batch.length;
                continue;
            }

            // Generate embeddings for the entire batch
            const texts = validChunks.map(chunk => chunk.content);
            const embeddings = await embeddingService.batchGenerateEmbeddings(texts);

            if (embeddings.length !== validChunks.length) {
                console.warn(`    ⚠️ Mismatch: got ${embeddings.length} embeddings for ${validChunks.length} chunks`);
            }

            // Store embeddings in database
            const insertPromises = validChunks.map(async (chunk, index) => {
                if (index >= embeddings.length) {
                    console.warn(`    ⚠️ No embedding for chunk: ${chunk.id}`);
                    return false;
                }

                const embedding = embeddings[index];
                if (!embedding || embedding.length === 0) {
                    console.warn(`    ⚠️ Empty embedding for chunk: ${chunk.id}`);
                    return false;
                }

                const { error } = await supabase
                    .from('document_embeddings')
                    .insert({
                        document_id: chunk.id,
                        content: chunk.content,
                        metadata: chunk.metadata as Json,
                        embedding: `[${embedding.join(',')}]`
                    });

                if (error) {
                    console.error(`    ❌ Error storing chunk ${chunk.id}:`, error);
                    return false;
                }

                return true;
            });

            const results = await Promise.all(insertPromises);
            const batchSuccessCount = results.filter(Boolean).length;
            successCount += batchSuccessCount;
            processedCount += batch.length;

            console.log(`    ✅ Batch ${batchNum} complete: ${batchSuccessCount}/${validChunks.length} successful`);

            // Add delay between batches to avoid rate limiting (1 second delay)
            if (batchNum < totalBatches) {
                console.log(`    ⏳ Waiting 1 second before next batch...`);
                await sleep(1000);
            }

        } catch (error) {
            console.error(`    ❌ Error processing batch ${batchNum}:`, error);
            processedCount += batch.length;

            // If we hit rate limit, wait longer
            if (error.message && error.message.includes('RATE_LIMIT_EXCEEDED')) {
                console.log(`    ⏳ Rate limit hit, waiting 60 seconds...`);
                await sleep(60000);
            }
        }
    }

    // 6. Verify results
    console.log("🔍 Verifying results...");
    const { data: storedEmbeddings, error: countError } = await supabase
        .from('document_embeddings')
        .select('id, metadata')
        .limit(1000);

    if (countError) {
        console.error("❌ Error verifying embeddings:", countError);
        return;
    }

    // Count documents with technology metadata
    const techDocs = storedEmbeddings?.filter(doc =>
        doc.metadata && (
            (doc.metadata as any).technology ||
            (doc.metadata as any).technologies
        )
    ) || [];

    console.log("📊 Regeneration Results:");
    console.log(`   Total chunks processed: ${processedCount}`);
    console.log(`   Successful embeddings: ${successCount}`);
    console.log(`   Total stored embeddings: ${storedEmbeddings?.length || 0}`);
    console.log(`   Documents with technology metadata: ${techDocs.length}`);

    // Show sample technology metadata
    if (techDocs.length > 0) {
        console.log("🔍 Sample technology metadata:");
        techDocs.slice(0, 5).forEach((doc, idx) => {
            console.log(`   ${idx + 1}. ${doc.id}:`, (doc.metadata as any).technology || (doc.metadata as any).technologies);
        });
    }

    console.log("✅ Embedding regeneration complete!");
}

// Run the script
regenerateEmbeddings().catch(console.error); 