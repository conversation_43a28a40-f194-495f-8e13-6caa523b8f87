
# Deployment Instructions

This guide provides step-by-step instructions for deploying the Maksym Ionutsa AI Portfolio application to production.

## Prerequisites

Before deploying, ensure you have:
- Node.js 18+ installed
- A Supabase account and project
- A Google AI Studio account for Gemini API
- Access to a deployment platform (Vercel, Netlify, etc.)

## Step 1: Environment Setup

### 1.1 Clone and Install Dependencies

```bash
git clone https://github.com/peekknuf/context-whisper-limit.git
cd context-whisper-limit
pnpm install
```

### 1.2 Configure Environment Variables

1. Copy the example environment file:
   ```bash
   cp .env.example .env
   ```

2. Fill in your environment variables in `.env`:

```env
# Frontend Environment Variables (VITE_ prefix required for Vite)
VITE_SUPABASE_URL=https://your-project-id.supabase.co
VITE_SUPABASE_ANON_KEY=your_supabase_anon_key_here
VITE_GEMINI_API_KEY=your_gemini_api_key_here

# Backend/Script Environment Variables (for precompute-embeddings script)
SUPABASE_URL=https://your-project-id.supabase.co
SUPABASE_ANON_KEY=your_supabase_anon_key_here
GEMINI_API_KEY=your_gemini_api_key_here
```

### 1.3 Get Required API Keys

#### Supabase Credentials:
1. Go to [Supabase Dashboard](https://supabase.com/dashboard)
2. Create a new project or select your existing project
3. Navigate to Settings → API
4. Copy your Project URL and anon/public key
5. Update both `VITE_SUPABASE_URL` & `SUPABASE_URL` and `VITE_SUPABASE_ANON_KEY` & `SUPABASE_ANON_KEY` in your `.env` file

#### Google Gemini API Key:
1. Visit [Google AI Studio](https://aistudio.google.com/app/apikey)
2. Create a new API key
3. Update both `VITE_GEMINI_API_KEY` and `GEMINI_API_KEY` in your `.env` file

## Step 2: Database Setup

### 2.1 Run Database Migrations

The database schema is already set up through Supabase migrations. Ensure your Supabase project includes:

- `document_embeddings` table with pgvector extension
- `query_embeddings_cache` table for query caching
- `chat_sessions` table for conversation history
- Required database functions for vector similarity search
- Proper RLS (Row Level Security) policies

### 2.2 Verify Database Schema

Check that these tables exist in your Supabase project:

```sql
-- Verify tables exist
SELECT table_name FROM information_schema.tables 
WHERE table_schema = 'public' 
AND table_name IN ('document_embeddings', 'query_embeddings_cache', 'chat_sessions');

-- Verify pgvector extension
SELECT * FROM pg_extension WHERE extname = 'vector';
```

## Step 3: Data Preparation

### 3.1 Prepare Documents

1. Ensure your documents are in the `public/documents/code/` directory
2. The application includes these default documents:
   - `identity-and-contact.json`
   - `technical-skills.json`
   - `work-experience.json`
   - `projects-showcase.json`
   - `education-and-qa.json`

### 3.2 Precompute Embeddings (Optional but Recommended)

For optimal performance, precompute document embeddings:

```bash
# Install dependencies for the embedding script
pnpm install @huggingface/transformers

# Run the precomputation script
pnpm run precompute-embeddings
```

**Note**: This step requires significant computational resources and may take time on the first run.

## Step 4: Local Testing

### 4.1 Test Development Build

```bash
pnpm run dev
```

Verify that:
- The application loads without errors
- Chat functionality works with your Gemini API key
- Document retrieval and RAG responses function correctly
- All environment variables are properly configured

### 4.2 Test Production Build

```bash
pnpm run build
pnpm run preview
```

Ensure the production build works correctly before deploying.

## Step 5: Production Deployment

### Option A: Vercel Deployment

1. **Connect to Vercel:**
   ```bash
   pnpm install -g vercel
   vercel login
   vercel
   ```

2. **Configure Environment Variables in Vercel:**
   - Go to your Vercel project dashboard
   - Navigate to Settings → Environment Variables
   - Add all variables from your `.env` file:
     - `VITE_SUPABASE_URL`
     - `VITE_SUPABASE_ANON_KEY`
     - `VITE_GEMINI_API_KEY`
     - `SUPABASE_URL`
     - `SUPABASE_ANON_KEY`
     - `GEMINI_API_KEY`

3. **Deploy:**
   ```bash
   vercel --prod
   ```

### Option B: Netlify Deployment

1. **Build and Deploy:**
   ```bash
   pnpm run build
   ```

2. **Upload to Netlify:**
   - Drag and drop the `dist` folder to Netlify
   - Or connect your Git repository for automatic deployments

3. **Configure Environment Variables:**
   - Go to Site settings → Environment variables
   - Add all required environment variables

### Option C: Custom Server Deployment

1. **Build the application:**
   ```bash
   pnpm run build
   ```

2. **Serve the static files:**
   ```bash
   # Using a simple HTTP server
   npx serve dist
   
   # Or configure your web server (Apache, Nginx) to serve the dist directory
   ```

3. **Configure environment variables** on your server or hosting platform

## Step 6: Post-Deployment Configuration

### 6.1 Domain Configuration

If using a custom domain:
1. Configure DNS settings to point to your deployment platform
2. Set up SSL certificates (usually automatic with Vercel/Netlify)
3. Update any hardcoded URLs if necessary

### 6.2 Performance Optimization

1. **Enable HNSW Indexing** (if not automatically applied):
   ```sql
   CREATE INDEX IF NOT EXISTS document_embeddings_embedding_hnsw_idx 
   ON public.document_embeddings 
   USING hnsw (embedding vector_cosine_ops)
   WITH (m = 16, ef_construction = 64);
   ```

2. **Monitor Performance:**
   - Check Supabase dashboard for query performance
   - Monitor Gemini API usage and costs
   - Set up error tracking if needed

### 6.3 Content Updates

To update the AI's knowledge base:
1. Modify documents in `public/documents/code/`
2. Re-run the embedding precomputation script
3. Redeploy the application

## Step 7: Monitoring and Maintenance

### 7.1 Regular Maintenance Tasks

1. **Monitor API Usage:**
   - Check Gemini API quota and billing
   - Monitor Supabase usage and performance

2. **Database Maintenance:**
   ```sql
   -- Clean up old query cache entries (run monthly)
   SELECT cleanup_query_cache(30, 2);
   ```

3. **Content Updates:**
   - Update personal information in document files
   - Add new projects and experience
   - Re-run embedding precomputation when content changes

### 7.2 Troubleshooting

**Common Issues:**

1. **Chat not working:**
   - Verify `VITE_GEMINI_API_KEY` and `GEMINI_API_KEY` are correctly set
   - Check Gemini API quotas and billing

2. **No contextual responses:**
   - Ensure document embeddings are precomputed
   - Verify Supabase connection and database schema

3. **Build errors:**
   - Check all environment variables are set
   - Verify all dependencies are installed
   - Clear node_modules and reinstall if needed

4. **Performance issues:**
   - Monitor database query performance in Supabase
   - Consider upgrading Supabase plan for better performance
   - Optimize embedding cache usage

## Security Considerations

1. **Environment Variables:**
   - Never commit `.env` files to version control
   - Use secure environment variable management in production
   - Regularly rotate API keys

2. **Database Security:**
   - RLS policies are properly configured
   - Monitor access logs in Supabase
   - Regular security updates

3. **API Keys:**
   - Restrict API key permissions where possible
   - Monitor API usage for unusual activity
   - Set up billing alerts

## Performance Expectations

After proper deployment, expect:
- **Initial page load:** < 3 seconds
- **Chat response time:** 2-10 seconds (depending on query complexity)
- **Cached responses:** < 1 second
- **Vector search:** < 100ms for similar document retrieval

## Support and Updates

For ongoing support:
1. Monitor application logs and error tracking
2. Keep dependencies updated regularly
3. Follow Supabase and Gemini API updates for new features
4. Update document content to keep information current

---

**Deployment Checklist:**

- [ ] Environment variables configured
- [ ] Supabase project set up with correct schema
- [ ] Gemini API key obtained and tested
- [ ] Documents prepared and embeddings precomputed
- [ ] Local testing completed successfully
- [ ] Production build tested
- [ ] Deployment platform configured
- [ ] Custom domain configured (if applicable)
- [ ] Performance monitoring set up
- [ ] Security considerations reviewed

