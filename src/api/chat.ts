import { GoogleGenerativeAI } from "@google/generative-ai";
import { supabase } from "@/integrations/supabase/client";
import { QueryEmbeddingCache, EmbeddingService, IntentCache, runRagPipeline } from '@/lib/rag';
import { IntentAnalysis } from '@/types/chat';

const apiKey = (() => {
    const k = import.meta.env.VITE_GEMINI_API_KEY22 || import.meta.env.VITE_GEMINI_API_KEY;
  if (!k) throw new Error("VITE_GEMINI_API_KEY or VITE_GEMINI_API_KEY22 is required");
  return k;
})();

const genAI = new GoogleGenerativeAI(apiKey);

// Instantiate Cache and Service
const embeddingService = new EmbeddingService();
const queryEmbeddingCache = new QueryEmbeddingCache(embeddingService);
const intentCache = new IntentCache();
let embeddingServiceInitialized = false;

async function ensureEmbeddingServiceInitialized() {
  if (!embeddingServiceInitialized) {
    try {
      await embeddingService.initialize();
      embeddingServiceInitialized = true;
      console.log("✅ EmbeddingService initialized successfully in chat.ts.");
    } catch (e) {
      console.error("❌ Failed to initialize EmbeddingService in chat.ts:", e);
      throw e;
    }
  }
}

export async function* streamChatWithGemini(messages: Array<{ role: string, content: string }>, signal?: AbortSignal) {
  try {
    await ensureEmbeddingServiceInitialized();

    const query = messages[messages.length - 1].content;
    const sessionId = crypto.randomUUID();

    console.log(`🔍 Processing query (streaming) for session ${sessionId}:`, query);

    const responseGenerator = runRagPipeline(
      query,
      messages,
      embeddingService,
      queryEmbeddingCache,
      intentCache,
      genAI,
      supabase,
      sessionId, // Pass the session ID
      signal
    );

    for await (const chunk of responseGenerator) {
      yield chunk;
    }

    console.log("✅ Response stream finished");

  } catch (error: unknown) {
    console.error("Error in streaming response:", error);
    if (error instanceof Error && error.message?.includes("API key not valid")) {
      throw new Error("Invalid API Key for Google Gemini.");
    }
    throw new Error("Failed to generate streaming response from Gemini.");
  }
}
