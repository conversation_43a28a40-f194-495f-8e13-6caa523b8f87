import { PageLayout } from "@/components/common/PageLayout";
import { SITE_CONFIG } from "@/lib/constants";
import { Calendar, BarChart3, ExternalLink } from "lucide-react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";

const visualizations = [
  {
    id: "covid-dashboard",
    title: "COVID-19 Global Dashboard",
    description: "Interactive dashboard analyzing global COVID-19 trends, death rates, and geographical distribution. Features dynamic filtering, time-series analysis, and comparative metrics across countries and regions.",
    tools: ["Tableau", "Data Visualization", "Public Health Analytics"],
    link: "https://public.tableau.com/views/Covid_19_data_16736588526850/Dashboard1?:language=en-US&:sid=&:redirect=auth&:display_count=n&:origin=viz_share_link",
    highlights: [
      "Real-time global COVID-19 data visualization",
      "Interactive geographical mapping with heat maps",
      "Time-series trend analysis and forecasting",
      "Comparative metrics across multiple dimensions"
    ]
  },
  {
    id: "hr-dashboard",
    title: "HR Analytics Dashboard",
    description: "Comprehensive human resources analytics dashboard providing insights into employee performance, satisfaction, and organizational metrics. Includes workforce analytics, retention analysis, and performance tracking.",
    tools: ["Tableau", "HR Analytics", "Workforce Intelligence"],
    link: "https://public.tableau.com/views/HRreport_16940327590050/Dashboard1?:language=en-US&:sid=&:redirect=auth&:display_count=n&:origin=viz_share_link",
    highlights: [
      "Employee performance and satisfaction metrics",
      "Workforce demographics and diversity analysis",
      "Retention and turnover rate tracking",
      "Department-wise performance comparisons"
    ]
  },
  {
    id: "churn-analytics",
    title: "Customer Churn Analytics",
    description: "Advanced customer churn prediction and analysis dashboard identifying at-risk customers and factors contributing to customer attrition. Features predictive modeling and customer segmentation.",
    tools: ["Tableau", "Predictive Analytics", "Customer Intelligence"],
    link: "https://public.tableau.com/views/ChurnAnalytics_16812536285050/Dashboard1?:language=en-US&:sid=&:redirect=auth&:display_count=n&:origin=viz_share_link",
    highlights: [
      "Customer churn prediction modeling",
      "Risk factor identification and analysis",
      "Customer segmentation and profiling",
      "Retention strategy recommendations"
    ]
  },
  {
    id: "profits-dashboard",
    title: "Business Profits Dashboard",
    description: "Executive-level profits and financial performance dashboard showcasing revenue trends, profit margins, and business unit performance. Includes forecasting and variance analysis.",
    tools: ["Tableau", "Financial Analytics", "Business Intelligence"],
    link: "https://public.tableau.com/views/ProfitsDash/Dashboard1?:language=en-US&:sid=&:redirect=auth&:display_count=n&:origin=viz_share_link",
    highlights: [
      "Revenue and profit trend analysis",
      "Business unit performance comparison",
      "Financial forecasting and projections",
      "Variance analysis and KPI tracking"
    ]
  }
];

export default function Vizzes() {
  return (
    <PageLayout currentPage="vizzes">
      {/* Page Header */}
      <div className="mb-8">
        <h1 className="text-4xl font-bold mb-4">Data Visualizations</h1>
        <div className="flex items-center gap-2 text-zinc-500 dark:text-zinc-500 text-sm">
          <Calendar className="h-4 w-4" />
          May 17, 2025 · 3 min · {SITE_CONFIG.name}
        </div>
      </div>

      {/* Introduction */}
      <div className="mb-8">
        <p className="text-zinc-700 dark:text-zinc-300 leading-relaxed text-lg">
          Interactive data visualizations and dashboards showcasing analytical insights
          across various domains. Built using modern BI tools to transform raw data
          into actionable intelligence.
        </p>
      </div>

      {/* Visualizations */}
      <div className="grid gap-8 md:grid-cols-2">
        {visualizations.map((viz) => (
          <Card key={viz.id} className="h-fit bg-sidebar-accent border border-sidebar-border">
            <CardHeader>
              <div className="flex items-start justify-between gap-4">
                <div className="flex items-center gap-3">
                  <div className="p-2 rounded-lg bg-sidebar-primary">
                    <BarChart3 className="h-6 w-6 text-sidebar-accent-foreground" />
                  </div>
                  <div>
                    <CardTitle className="text-xl text-foreground">{viz.title}</CardTitle>
                    <CardDescription className="mt-1">
                      {viz.description}
                    </CardDescription>
                  </div>
                </div>
              </div>
            </CardHeader>

            <CardContent className="space-y-4">
              {/* Tools */}
              <div>
                <h4 className="font-semibold text-zinc-800 dark:text-zinc-200 mb-2">
                  Tools & Technologies
                </h4>
                <div className="flex flex-wrap gap-2">
                  {viz.tools.map((tool) => (
                    <Badge key={tool} variant="secondary" className="text-xs">
                      {tool}
                    </Badge>
                  ))}
                </div>
              </div>

              {/* Key Features */}
              <div>
                <h4 className="font-semibold text-zinc-800 dark:text-zinc-200 mb-3">
                  Key Features
                </h4>
                <ul className="space-y-2">
                  {viz.highlights.map((highlight, index) => (
                    <li key={index} className="flex items-start gap-2 text-sm text-zinc-700 dark:text-zinc-300">
                      <div className="w-1.5 h-1.5 bg-sidebar-foreground rounded-full mt-2 flex-shrink-0" />
                      <span>{highlight}</span>
                    </li>
                  ))}
                </ul>
              </div>

              {/* View Link */}
              <div className="pt-2">
                <a
                  href={viz.link}
                  className="inline-flex items-center gap-2 px-4 py-2 text-sm bg-sidebar-foreground text-sidebar-primary-foreground rounded-lg hover:bg-foreground hover:text-background transition-colors"
                  target="_blank"
                  rel="noopener noreferrer"
                >
                  <ExternalLink className="h-4 w-4" />
                  View Interactive Dashboard
                </a>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    </PageLayout>
  );
}
