import { useState, useEffect, useRef } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Textarea } from "@/components/ui/textarea";
import { Send, Loader2 } from "lucide-react";
import { streamChatWithGemini } from "@/api/chat";
import { PageLayout } from "@/components/common/PageLayout";
import { MarkdownRenderer } from "@/components/common/MarkdownRenderer";

interface Message {
  role: "user" | "assistant";
  content: string;
}

const Index = () => {
  const [messages, setMessages] = useState<Message[]>([]);
  const [input, setInput] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const textareaRef = useRef<HTMLTextAreaElement>(null);

  const initialAssistantMessage = "Hey!\n\nI'm an AI assistant that can tell you about <PERSON><PERSON><PERSON>'s skills, experience, and projects. I can help you understand if he would be a good fit for your role.\n\nWhat would you like to know? Feel free to drop a job description with the requirements and I'll do my best to answer your questions.";

  // Auto resize textarea to fit content - improved logic
  useEffect(() => {
    if (textareaRef.current) {
      const textarea = textareaRef.current;
      textarea.style.height = 'auto';
      textarea.style.height = `${textarea.scrollHeight}px`;
    }
  }, [input]);

  const handleSendMessage = async () => {
    if (!input.trim()) return;
    const userMessage: Message = { role: "user", content: input };
    const newMessages = [...messages, userMessage];
    setMessages(newMessages); // Add user message immediately
    setInput("");
    setIsLoading(true);

    try {
      const assistantMessage: Message = { role: "assistant", content: "" };
      setMessages(prev => [...prev, assistantMessage]); // Add an empty assistant message to start with

      const stream = streamChatWithGemini(newMessages);
      let accumulatedContent = '';
      for await (const chunk of stream) {
        accumulatedContent += chunk;
        setMessages(prev => {
          const lastMessage = prev[prev.length - 1];
          if (lastMessage.role === "assistant") {
            return [...prev.slice(0, -1), { ...lastMessage, content: accumulatedContent }];
          }
          return [...prev, { role: "assistant", content: accumulatedContent }]; // Fallback, should not happen if initial empty message is added
        });
      }
    } catch (error: unknown) {
      const errorMessage: Message = {
        role: "assistant",
        content: `Error: ${error.message}`,
      };
      setMessages(prev => [...prev, errorMessage]);
    } finally {
      setIsLoading(false);
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  return (
    <PageLayout currentPage="chat" showBreadcrumb={false}>
      <div className="max-w-2xl mx-auto">
        {/* Page Title */}
        <div className="text-center mb-8">
          <h1 className="text-4xl font-bold mb-4">Chat about Me</h1>
          <p className="text-zinc-600 dark:text-zinc-400 text-lg">Ask me anything about my background</p>
        </div>

        {/* Initial Assistant Introduction */}
        <div className="mb-8 text-zinc-900 dark:text-white">
          <div className="prose prose-p:mt-4">
            <MarkdownRenderer content={initialAssistantMessage} />
          </div>
        </div>

        {/* Chat Messages */}
        <div className="space-y-6 mb-8">
          {messages.map((message, index) => (
            <div key={index} className="space-y-2">
              {message.role === "assistant" && (
                <MarkdownRenderer content={message.content} />
              )}
              {message.role === "user" && (
                <div className="bg-zinc-200 dark:bg-zinc-700 text-zinc-900 dark:text-white p-3 rounded-lg max-w-md ml-auto min-h-[40px] overflow-auto leading-tight text-base break-words whitespace-pre-wrap">
                  <span>{message.content}</span>
                </div>
              )}
            </div>
          ))}
          {isLoading && (
            <div className="flex items-center space-x-2 text-zinc-500 dark:text-zinc-400">
              <Loader2 className="h-4 w-4 animate-spin" />
              <span>Thinking...</span>
            </div>
          )}
        </div>

        {/* Input Area */}
        <div className="max-w-2xl mx-auto">
          <div className="relative">
            <Textarea
              ref={textareaRef}
              placeholder={"Type your message here..."}
              value={input}
              onChange={(e) => setInput(e.target.value)}
              onKeyPress={handleKeyPress}
              className="bg-white dark:bg-zinc-800 border-zinc-300 dark:border-zinc-600 text-zinc-900 dark:text-white placeholder-zinc-400 pr-12 resize-none min-h-[60px]"
              rows={1}
              disabled={false}
              style={{
                lineHeight: "1.5",
                overflow: "hidden"
              }}
            />
            <Button
              onClick={handleSendMessage}
              disabled={isLoading || !input.trim()}
              className="absolute bottom-2 right-2 bg-zinc-400 hover:bg-zinc-500 dark:bg-zinc-500 dark:hover:bg-zinc-400"
              size="sm"
            >
              <Send className="h-4 w-4" />
            </Button>
          </div>
          <div className="text-center mt-2 text-sm text-zinc-500">
            Press{" "}
            <kbd className="px-1 py-0.5 bg-zinc-200 dark:bg-zinc-700 rounded text-xs">Enter</kbd> to send,{" "}
            <kbd className="px-1 py-0.5 bg-zinc-200 dark:bg-zinc-700 rounded text-xs">Shift + Enter</kbd> for new line
          </div>
        </div>
      </div>
    </PageLayout>
  );
};

export default Index;
