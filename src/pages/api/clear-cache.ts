import type { APIRoute } from 'astro';
import { clearLruCache } from '@/lib/rag/queryEmbeddingCache';

export const GET: APIRoute = ({ request }) => {
  try {
    clearLruCache();
    return new Response(JSON.stringify({ message: 'In-memory cache cleared successfully.' }), {
      status: 200,
      headers: { 'Content-Type': 'application/json' }
    });
  } catch (error) {
    console.error('Error clearing cache:', error);
    return new Response(JSON.stringify({ message: 'Failed to clear cache.' }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
};
