import { PageLayout } from "@/components/common/PageLayout";
import { SITE_CONFIG } from "@/lib/constants";

export default function Blog() {
  return (
    <PageLayout currentPage="blog">
      {/* Page Header */}
      <div className="mb-8">
        <h1 className="text-4xl font-bold mb-2">Blog</h1>
        <div className="text-zinc-500 dark:text-zinc-500 text-sm">
          May 17, 2025 · {SITE_CONFIG.name}
        </div>
      </div>

      {/* Coming Soon */}
      <div className="text-center py-16">
        <h2 className="text-2xl font-semibold mb-4 text-zinc-700 dark:text-zinc-300">
          Coming Soon
        </h2>
        <p className="text-zinc-600 dark:text-zinc-400">
          A day may come when something will get posted here, but it is not this day
        </p>
      </div>
    </PageLayout>
  );
}
