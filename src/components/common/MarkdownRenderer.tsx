import React from "react";
import ReactMarkdown from "react-markdown";
import { Prism as Syntax<PERSON>ighlighter } from "react-syntax-highlighter";
import { vscDarkPlus } from "react-syntax-highlighter/dist/esm/styles/prism";
import { oneLight } from "react-syntax-highlighter/dist/esm/styles/prism";

interface MarkdownRendererProps {
  content: string;
}

export const MarkdownRenderer = ({ content }: MarkdownRendererProps) => {
  // Detect system theme for code block styling
  const isDarkMode = window.matchMedia &&
    window.matchMedia("(prefers-color-scheme: dark)").matches;

  return (
    <div className="prose prose-zinc dark:prose-invert max-w-none prose-p:leading-7 prose-headings:leading-7 prose-li:leading-7 prose-p:mt-6 prose-p:mb-4 prose-headings:my-6 prose-ul:my-4 prose-ol:my-4 prose-li:my-2 prose-h3:font-bold prose-h3:text-xl">
      <ReactMarkdown
        components={{
          p({ node, ...props }) {
            return <p className="mb-4" {...props} />;
          },
          h3({ node, ...props }) {
            return <h3 className="font-bold text-xl mt-8 mb-2" {...props} />;
          },
          code(props) {
            const { children, className, ...rest } = props;
            const match = /language-(\w+)/.exec(className || "");
            return match ? (
              <SyntaxHighlighter
                style={isDarkMode ? vscDarkPlus : oneLight}
                language={match[1]}
                PreTag="div"
                customStyle={{
                  borderRadius: "0.5rem",
                  fontSize: "0.95em",
                  padding: "1em",
                  margin: "1.5em 0"
                }}
                {...rest}
              >
                {String(children).replace(/\n$/, "")}
              </SyntaxHighlighter>
            ) : (
              <code
                className="bg-zinc-200 dark:bg-zinc-700 rounded px-1 py-0.5 text-sm"
                {...rest}
              >
                {children}
              </code>
            );
          },
          table: ({ node, ...props }) => (
            <table className="table-auto w-full border-collapse border border-zinc-400 dark:border-zinc-700" {...props} />
          ),
          th: ({ node, ...props }) => (
            <th className="border border-zinc-400 dark:border-zinc-700 px-4 py-2 text-left bg-zinc-100 dark:bg-zinc-800" {...props} />
          ),
          td: ({ node, ...props }) => (
            <td className="border border-zinc-400 dark:border-zinc-700 px-4 py-2" {...props} />
          ),
        }}
      >
        {content}
      </ReactMarkdown>
    </div>
  );
};
