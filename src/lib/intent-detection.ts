export const roleFitPatterns = [
    // Direct role fit keywords
    /\b(fit|suitable|qualified|role|position|job|candidate|hire|assess|evaluation|match|requirements|skills gap|suitability)\b/,
    // Job description indicators
    /\b(we're looking for|we need|seeking|hiring|recruiting|vacancy|opening)\b/,
    // Job requirements and responsibilities
    /\b(responsibilities|duties|requirements|qualifications|experience needed|skills needed)\b/,
    // Job-related content patterns
    /\b(content operations|data management|process improvement|workflow|automation|mentor|stakeholder)\b/,
    // Job posting structure indicators
    /\b(you will|you'll be|you have|you're comfortable|you value|you love|you see)\b/,
    // Company culture and work style indicators
    /\b(remote|hybrid|on-site|team|collaboration|ownership|feedback|growth)\b/,
    // Years of experience indicators
    /\b(\d+\+?\s*years?\s*of?\s*experience)\b/,
    // Job title patterns
    /\b(analyst|specialist|lead|manager|engineer|coordinator|operator)\b/
  ];

export function detectTechnology(lowerQuery: string): string | undefined {
    if (/\b(sql|query|database|select|insert|update|delete|join)\b/.test(lowerQuery)) {
        return 'sql';
    } else if (/\b(python|pandas|numpy|dataframe|jupyter|py)\b/.test(lowerQuery)) {
        return 'python';
    } else if (/\b(go|golang|cli|goroutine|channel)\b/.test(lowerQuery)) {
        return 'golang';
    } else if (/\b(kafka|streaming|pipeline|real-time|event)\b/.test(lowerQuery)) {
        return 'streaming';
    } else if (/\b(javascript|js|node|react|typescript|ts)\b/.test(lowerQuery)) {
        return 'javascript';
    }
}