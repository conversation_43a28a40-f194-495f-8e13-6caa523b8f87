import { supabase } from "@/integrations/supabase/client";

export async function callRpc<T>(functionName: string, params: Record<string, unknown>): Promise<T | null> {
  try {
    const { data, error } = await supabase.rpc(functionName, params);

    if (error) {
      console.error(`❌ RPC call to ${functionName} failed:`, error);
      throw new Error(`RPC call to ${functionName} failed: ${error.message}`);
    }
    return data as T;
  } catch (error) {
    console.error(`❌ Exception during RPC call to ${functionName}:`, error);
    throw error;
  }
}
