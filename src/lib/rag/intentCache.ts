import { LRUCache } from 'lru-cache';
import { IntentAnalysis } from '@/types/chat';

// Simple, non-cryptographic hash function for strings
function simpleHash(str: string): string {
  let hash = 0;
  for (let i = 0; i < str.length; i++) {
    const char = str.charCodeAt(i);
    hash = ((hash << 5) - hash) + char;
    hash |= 0; // Convert to 32bit integer
  }
  return hash.toString();
}

export function stableHash(obj: unknown): string {
  // For this specific use case (query and stopWords), a simple stringification and hash is sufficient.
  // For more complex objects, a robust stable hashing library would be needed.
  return simpleHash(JSON.stringify(obj));
}

interface IntentCacheEntry {
  intent: IntentAnalysis;
  timestamp: number;
}

export class IntentCache {
  private cache: LRUCache<string, IntentCacheEntry>;
  private readonly TTL_MS = 5 * 60 * 1000; // 5 minutes TTL for intent cache

  constructor() {
    this.cache = new LRUCache<string, IntentCacheEntry>({
      max: 100, // Max 100 entries
      ttl: this.TTL_MS,
      updateAgeOnGet: true,
    });
    console.log("IntentCache initialized");
  }

  get(hash: string): IntentAnalysis | undefined {
    const entry = this.cache.get(hash);
    if (entry) {
      console.log(`[IntentCache] Cache hit for hash: ${hash}`);
      return entry.intent;
    }
    console.log(`[IntentCache] Cache miss for hash: ${hash}`);
    return undefined;
  }

  set(hash: string, intent: IntentAnalysis): void {
    this.cache.set(hash, { intent, timestamp: Date.now() });
    console.log(`[IntentCache] Cache set for hash: ${hash}`);
  }

  // Optional: cleanup method if needed, though LRUCache handles TTL automatically
  cleanup(): void {
    this.cache.prune();
    console.log("[IntentCache] Cache pruned.");
  }
}
