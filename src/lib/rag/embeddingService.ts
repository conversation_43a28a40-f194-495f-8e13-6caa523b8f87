import { GoogleGenerativeAI, GenerativeModel } from "@google/generative-ai";

export class EmbeddingService {
    private model: GenerativeModel | null = null;
    private isInitialized = false;

    async initialize(): Promise<void> {
        if (this.isInitialized) {
            return;
        }
        const apiKey = import.meta.env.VITE_GEMINI_API_KEY || import.meta.env.VITE_GEMINI_API_KEY22;
        if (!apiKey) {
            throw new Error("Gemini API key not found in environment variables. Make sure VITE_GEMINI_API_KEY or VITE_GEMINI_API_KEY22 is set.");
        }
        try {
            const genAI = new GoogleGenerativeAI(apiKey);
            this.model = genAI.getGenerativeModel({ model: "text-embedding-004" });
            this.isInitialized = true;
            console.log("Gemini embedding model initialized (text-embedding-004)");
        } catch (error) {
            console.error("Failed to initialize Gemini embedding model:", error);
            throw new Error("Failed to initialize Gemini embedding model");
        }
    }

    async generateEmbedding(text: string): Promise<number[]> {
        if (!this.isInitialized || !this.model) {
            throw new Error("Embedding service not initialized");
        }
        // Gemini's input token limit is 2048 tokens, so truncate if needed
        const maxLength = 8000; // Increased to ~2000 tokens (assuming ~4 chars/token)
        let truncatedText = text;
        if (text.length > maxLength) {
            truncatedText = text.substring(0, maxLength);
            console.warn(`⚠️ Truncating text for embedding. Original length: ${text.length}, Truncated length: ${maxLength}`);
        }
        try {
            const result = await this.model.embedContent(truncatedText);
            if (!result || !result.embedding || !result.embedding.values) {
                throw new Error("No embedding returned from Gemini API");
            }
            return result.embedding.values;
        } catch (error) {
            console.error("Error generating Gemini embedding:", error);
            throw new Error("Failed to generate Gemini embedding");
        }
    }

    async batchGenerateEmbeddings(texts: string[]): Promise<number[][]> {
        if (!this.isInitialized || !this.model) {
            throw new Error("Embedding service not initialized");
        }

        if (texts.length === 0) {
            return [];
        }

        // Truncate texts if needed (same logic as single embedding)
        const maxLength = 8000;
        const truncatedTexts = texts.map(text => {
            if (text.length > maxLength) {
                console.warn(`⚠️ Truncating text for batch embedding. Original length: ${text.length}, Truncated length: ${maxLength}`);
                return text.substring(0, maxLength);
            }
            return text;
        });

        try {
            // Convert texts to the correct format for batch embedding
            const requests = truncatedTexts.map(text => ({
                content: {
                    role: "user",
                    parts: [{ text }]
                }
            }));
            const result = await this.model.batchEmbedContents({ requests });

            if (!result || !result.embeddings) {
                throw new Error("No embeddings returned from Gemini API");
            }

            return result.embeddings.map(embedding => {
                if (!embedding.values) {
                    throw new Error("Invalid embedding returned from Gemini API");
                }
                return embedding.values;
            });
        } catch (error) {
            console.error("Error generating batch Gemini embeddings:", error);
            throw new Error("Failed to generate batch Gemini embeddings");
        }
    }

    dispose(): void {
        this.model = null;
        this.isInitialized = false;
    }
}