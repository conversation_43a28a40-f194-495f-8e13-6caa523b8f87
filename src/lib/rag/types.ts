
export interface RAGDocument {
  id: string;
  content: string;
  metadata: {
    type: "personal_info" | "project_readme" | "code";
    filename: string;
    project?: string;
    relative_path?: string;
    technology?: string;
    technologies?: string[];
    [key: string]: unknown;
  };
}

export interface RetrievedDocument {
  content: string;
  metadata: Record<string, unknown>;
  similarity: number;
}

export interface StoredEmbeddings {
  documents: RAGDocument[];
  embeddings: number[][];
}
