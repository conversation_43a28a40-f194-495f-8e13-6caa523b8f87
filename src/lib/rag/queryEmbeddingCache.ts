import { supabase } from "@/integrations/supabase/client";
import { EmbeddingService } from "./embeddingService"; // Import EmbeddingService

// Simple LRU implementation for in-memory cache for responses
const LRU_CACHE_MAX = 128;
const lruResponseCache: Map<string, string> = new Map(); // Stores query_hash -> cached_response

function setLRUResponse(key: string, value: string) {
  if (lruResponseCache.has(key)) {
    lruResponseCache.delete(key);
  } else if (lruResponseCache.size >= LRU_CACHE_MAX) {
    // Remove oldest entry
    const oldestKey = lruResponseCache.keys().next().value;
    lruResponseCache.delete(oldestKey);
  }
  lruResponseCache.set(key, value);
}

function getLRUResponse(key: string): string | undefined {
  if (!lruResponseCache.has(key)) return undefined;
  const val = lruResponseCache.get(key)!;
  // Refresh key
  lruResponseCache.delete(key);
  lruResponseCache.set(key, val);
  return val;
}

export function clearLruCache() {
  lruResponseCache.clear();
  console.log("🧹 In-memory LRU cache cleared.");
}

// Expose the clear function to the window for easy debugging
if (typeof window !== 'undefined') {
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  (window as any).clearCache = clearLruCache;
}

export class QueryEmbeddingCache {
  private embeddingService: EmbeddingService; // Inject EmbeddingService

  constructor(embeddingService: EmbeddingService) {
    this.embeddingService = embeddingService;
  }

  // Generate a simple hash for query caching
  private generateQueryHash(query: string): string {
    const normalized = query.toLowerCase().trim();
    console.log(`🔑 [Cache] Generating hash for normalized query: "${normalized.substring(0, 100)}..."`);

    try {
      // Encode the string to UTF-8, then convert to a binary string before btoa
      const utf8Encoded = encodeURIComponent(normalized).replace(/%([0-9A-F]{2})/g,
        function toSolidBytes(match, p1) {
          return String.fromCharCode(parseInt(p1, 16));
        });
      const hash = btoa(utf8Encoded).replace(/[^a-zA-Z0-9]/g, '').substring(0, 50);
      console.log(`🔑 [Cache] Generated hash: ${hash}`);
      return hash;
    } catch (error) {
      console.error("❌ [Cache] Error generating hash:", error);
      // Fallback to simple hash if btoa fails
      const fallbackHash = normalized.replace(/[^a-zA-Z0-9]/g, '').substring(0, 50);
      console.log(`🔑 [Cache] Using fallback hash: ${fallbackHash}`);
      return fallbackHash;
    }
  }

  // New method to get semantic cache hit
  async getSemanticCachedResponse(query: string, queryEmbedding: number[], queryHash: string): Promise<string | null> {
    // Check in-memory LRU first
    const memoryCachedResponse = getLRUResponse(queryHash);
    if (memoryCachedResponse) {
      console.log(`✅ [Semantic Cache] LRU cache HIT for hash: ${queryHash}`);
      return memoryCachedResponse;
    }

    try {
      const { data, error } = await supabase.rpc('match_cached_queries2', {
        query_embedding: `[${queryEmbedding.join(',')}]`,
        similarity_threshold: 0.9 // Use a reasonable default, can be configured
      });

      if (error) {
        console.error("❌ [Semantic Cache] Database match error:", error);
        return null;
      }

      if (data && data.length > 0) {
        const bestMatch = data[0];
        console.log(`✅ [Semantic Cache] Database HIT for query: "${query.substring(0, 50)}..." (Similarity: ${bestMatch.similarity.toFixed(3)})`);
        setLRUResponse(queryHash, bestMatch.cached_response);
        return bestMatch.cached_response;
      } else {
        console.log(`❌ [Semantic Cache] Database MISS for query: "${query.substring(0, 50)}..."`);
      }
    } catch (error) {
      console.error("❌ [Semantic Cache] Exception during semantic cache lookup:", error);
    }
    return null;
  }

  // New method to cache query embedding and its response
  async cacheQueryAndResponse(query: string, embedding: number[], response: string, queryHash: string): Promise<void> {
    const embeddingVector = `[${embedding.join(',')}]`;

    console.log(`💾 [Semantic Cache] Caching query and response for hash: ${queryHash}`);

    // Cache in-memory
    setLRUResponse(queryHash, response);

    try {
      // Direct insert into the table
      const { error } = await supabase
        .from('query_embeddings_cache')
        .insert({
          query_hash: queryHash,
          embedding: embeddingVector,
          cached_response: response,
          query_text: query, // Add this line
          access_count: 1, // Initialize access count
          last_accessed: new Date().toISOString() // Set current timestamp
        })
        .select(); // Use select to get data back if needed, or just insert

      if (error) {
        // Handle unique constraint violation (query_hash already exists)
        // In this case, we should update the existing entry instead of inserting
        if (error.code === '23505') { // PostgreSQL unique_violation error code
          console.warn(`��️ [Semantic Cache] Query hash ${queryHash} already exists. Attempting to update.`);
          const { error: updateError } = await supabase
            .from('query_embeddings_cache')
            .update({
              embedding: embeddingVector, // Update embedding in case it changed (unlikely for same hash)
              cached_response: response,
              query_text: query, // Add this line
              access_count: supabase.raw('access_count + 1'), // Increment access count
              last_accessed: new Date().toISOString()
            })
            .eq('query_hash', queryHash);

          if (updateError) {
            console.error("❌ [Semantic Cache] Error updating cached query:", updateError);
          } else {
            console.log(`✅ [Semantic Cache] Updated cached query for hash: ${queryHash}`);
          }
        } else {
          console.error("❌ [Semantic Cache] Error caching query and response:", error);
        }
      } else {
        console.log(`✅ [Semantic Cache] Query and response cached successfully for hash: ${queryHash}`);
      }
    } catch (error) {
      console.error("❌ [Semantic Cache] Exception caching query and response:", error);
    }
  }

  // Main function to get or generate response
  async* getOrGenerateResponse(
    query: string,
    generateEmbeddingFn: (text: string) => Promise<number[]>, // Function to generate embedding
    generateResponseFn: (messages: Array<{ role: "user" | "assistant"; content: string }>) => AsyncGenerator<string>, // Function to generate AI response
    messages: Array<{ role: "user" | "assistant"; content: string }> // Full message history for AI generation
  ): AsyncGenerator<string> {
    // 1. Generate hash and embedding
    const queryHash = this.generateQueryHash(query);
    const queryEmbedding = await generateEmbeddingFn(query);

    // 2. Check semantic cache for response
    const cachedResponse = await this.getSemanticCachedResponse(query, queryEmbedding, queryHash);
    if (cachedResponse) {
      console.log(`⚡️ [Semantic Cache] Returning cached response for query: "${query.substring(0, 50)}..."`);
      yield cachedResponse;
      return;
    }

    console.log(`🔄 [Semantic Cache] No semantic hit. Generating new response for query: "${query.substring(0, 50)}..."`);
    const responseStream = generateResponseFn(messages);
    let fullResponse = '';

    for await (const chunk of responseStream) {
      fullResponse += chunk;
      yield chunk;
    }

    await this.cacheQueryAndResponse(query, queryEmbedding, fullResponse, queryHash);
  }

  // Clean up old cache entries (for maintenance)
  async cleanupCache(daysOld: number = 30, minAccessCount: number = 2): Promise<number> {
    try {
      const { data, error } = await supabase.rpc('cleanup_query_cache', {
        days_old: daysOld,
        min_access_count: minAccessCount
      });

      if (error) {
        console.error("Error cleaning up cache:", error);
        return 0;
      }

      console.log(`Cleaned up ${data || 0} old cache entries`);
      return data || 0;
    } catch (error) {
      console.error("Error cleaning up cache:", error);
      return 0;
    }
  }
}
