import { GoogleGenerativeAI, GenerativeModel } from "@google/generative-ai";
import { SupabaseClient } from "@supabase/supabase-js";
import { QueryEmbeddingCache } from "./queryEmbeddingCache";
import { EmbeddingService } from "./embeddingService";
import { IntentCache, stableHash } from "./intentCache";
import { roleFitPatterns, detectTechnology } from "../intent-detection";
import { overarching_guidelines, intentSpecificTasks } from "../prompts";
import { callRpc } from "../supabase-rpc";

type SupabaseDocumentRow = {
  content: string;
  metadata: Record<string, unknown>;
  document_id: string;
  embedding: number[];
};

type RpcMatchResultRow = {
  content: string;
  metadata: Record<string, unknown>;
  document_id: string;
  similarity: number;
};

export type DocumentResult = {
  content: string;
  metadata: Record<string, unknown>;
  similarity: number;
  document_id?: string;
};

export interface IntentAnalysis {
  intent: string;
  needsComprehensiveProfile: boolean;
  needsCodeExamples: boolean;
  needsProjectDetails: boolean;
  detectedTechnology?: string;
}

interface ChatMessage {
  role: "user" | "assistant";
  content: string;
}

export function detectIntent(query: string, intentCache: IntentCache): IntentAnalysis {
  const intentHash = stableHash({ query });
  const cachedIntent = intentCache.get(intentHash);
  if (cachedIntent) {
    return cachedIntent;
  }

  const lowerQuery = query.toLowerCase();
  const queryLength = query.length;
  const hasRoleFitPatterns = roleFitPatterns.some(pattern => pattern.test(lowerQuery));
  const isLongQuery = queryLength > 200;
  const isRoleFit = hasRoleFitPatterns || (isLongQuery && queryLength > 300);

  console.log(`🔍 Intent Detection Debug:
    Query length: ${queryLength}
    Has role fit patterns: ${hasRoleFitPatterns}
    Is long query (>200): ${isLongQuery}
    Final role fit decision: ${isRoleFit}`);

  const isCodeInquiry = /\b(code|implementation|how to|show me|example|technical|algorithm|sql|python|golang|streaming|javascript)\b/.test(lowerQuery);
  const isProjectInquiry = /\b(project|built|worked on|developed|created|gengo|clarity|streaming pipeline)\b/.test(lowerQuery);
  const detectedTechnology = detectTechnology(lowerQuery);

  const intent = {
    intent: isRoleFit ? 'role_fit' : isCodeInquiry ? 'technical_inquiry' : isProjectInquiry ? 'specific_project' : 'general_profile',
    needsComprehensiveProfile: isRoleFit,
    needsCodeExamples: isCodeInquiry,
    needsProjectDetails: isProjectInquiry,
    detectedTechnology
  };

  intentCache.set(intentHash, intent);
  return intent;
}

async function vectorSearch(
  query: string,
  k: number,
  embeddingService: EmbeddingService,
  supabase: SupabaseClient,
  technology?: string
): Promise<DocumentResult[]> {
  try {
    console.log("🔍 Starting vector search for:", query);
    const t0 = performance.now();
    const queryEmbedding = await embeddingService.generateEmbedding(query);
    const t1 = performance.now();
    console.log(`[embedding] ${queryEmbedding.length} dims, took ${t1 - t0} ms`);

    if (!queryEmbedding || queryEmbedding.length === 0) {
      console.error("❌ Failed to get a valid query embedding.");
      return await fallbackSemanticSearch(query, supabase, k, technology);
    }

    console.log(`[pipeline/vectorSearch] Query embedding dimensions: ${queryEmbedding.length}`);

    const { data: embeddingCheck, error: checkError } = await supabase
      .from('document_embeddings')
      .select('embedding')
      .limit(1);

    if (checkError) {
      console.error("❌ Error checking document_embeddings table structure/accessibility:", checkError);
    }
    if (!embeddingCheck || embeddingCheck.length === 0) {
      console.warn("⚠️ No documents found in document_embeddings table. Vector search will likely yield no results.");
    }

    const data = await callRpc<RpcMatchResultRow[]>('match_documents_filtered', {
      query_embedding: queryEmbedding,
      match_threshold: 0.5,
      match_count: k * 2, // Fetch more results to account for potential filtering
      technology_filter: technology
    });

    if (!data || data.length === 0) {
      console.warn("⚠️ No vector search results found");
      console.log("⚠️ Falling back to semantic similarity search");
      return await fallbackSemanticSearch(query, supabase, k, technology);
    }

    let results: DocumentResult[] = data.map((row: RpcMatchResultRow) => ({
      content: row.content,
      metadata: row.metadata,
      similarity: row.similarity,
      document_id: row.document_id
    }));

    // Apply technology filtering in application layer
    if (technology) {
      const techLower = technology.toLowerCase();
      const originalCount = results.length;
      results = results.filter(doc => {
        // Check if the document content contains the technology keyword
        const contentLower = doc.content.toLowerCase();
        const filenameLower = (doc.metadata?.filename as string || '').toLowerCase();
        const projectLower = (doc.metadata?.project as string || '').toLowerCase();

        return contentLower.includes(techLower) ||
          filenameLower.includes(techLower) ||
          projectLower.includes(techLower);
      });
      console.log(`🔍 Technology filter "${technology}" reduced results from ${originalCount} to ${results.length}`);
    }

    // Limit to requested number of results
    results = results.slice(0, k);

    console.log(`📊 Vector search found ${results.length} documents`);
    return results;
  } catch (error) {
    console.error("❌ Vector search failed:", error);
    console.log("⚠️ Falling back to semantic similarity search");
    return await fallbackSemanticSearch(query, supabase, k, technology);
  }
}

function extractKeywords(query: string): string[] {
  const stopWords = new Set(['the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by', 'from', 'up', 'about', 'into', 'through', 'during', 'before', 'after', 'above', 'below', 'under', 'between', 'among', 'what', 'where', 'when', 'why', 'how', 'show', 'me', 'some', 'can', 'you', 'do', 'does', 'is', 'are', 'was', 'were', 'have', 'has', 'had']);
  return query
    .toLowerCase()
    .replace(/[^\w\s]/g, ' ')
    .split(/\s+/)
    .filter(word => word.length > 2 && !stopWords.has(word))
    .slice(0, 10);
}

async function fallbackSemanticSearch(
  query: string,
  supabase: SupabaseClient,
  limit: number,
  technology?: string
): Promise<DocumentResult[]> {
  console.log("Using semantic similarity fallback");
  const keywords = extractKeywords(query);
  console.log("Extracted keywords:", keywords);

  const results: SupabaseDocumentRow[] = await callRpc('keyword_search', {
    query_keywords: keywords,
    doc_limit: limit
  });

  console.log(`Semantic search found ${results?.length ?? 0} documents`);

  if (!results || results.length === 0) return [];

  const filteredResults = results
    .map(row => ({
      content: row.content,
      metadata: row.metadata,
      similarity: 0.5, // Placeholder similarity
      document_id: row.document_id
    }))
    .filter(doc => doc.content.length > 10);

  if (technology) {
    const techLower = technology.toLowerCase();
    return filteredResults.filter(doc => {
      const docTech = doc.metadata?.technology;
      const docTechs = doc.metadata?.technologies;
      if (typeof docTech === 'string' && docTech.toLowerCase() === techLower) {
        return true;
      }
      if (Array.isArray(docTechs)) {
        return docTechs.some(t => typeof t === 'string' && t.toLowerCase() === techLower);
      }
      return false;
    });
  }

  return filteredResults;
}

async function retrieveDocuments(
  query: string,
  intentAnalysis: IntentAnalysis,
  embeddingService: EmbeddingService,
  supabase: SupabaseClient,
  k: number
): Promise<DocumentResult[]> {
  console.log(`Enhanced vector search with intent: ${intentAnalysis.intent}`);

  try {
    const vectorSearchResults = await vectorSearch(query, k, embeddingService, supabase, intentAnalysis.detectedTechnology);
    if (vectorSearchResults.length > 0) {
      return vectorSearchResults;
    }
    console.warn("⚠️ No vector search results found");
  } catch (error) {
    console.error("Error during vector search:", error);
  }

  // Fallback to semantic search if vector search fails or returns no results
  console.warn("⚠️ Falling back to semantic similarity search");
  return await fallbackSemanticSearch(query, supabase, k * 2, intentAnalysis.detectedTechnology); // Fetch more results in fallback
}

export function formatContext(docs: DocumentResult[]): string {
  if (docs.length === 0) return "";
  return docs.map((doc, idx) => {
    const source = doc.metadata?.filename || doc.metadata?.relative_path || `Document ${idx + 1}`;
    return `### ${source}\n${doc.content}\n---`;
  }).join("\n\n");
}

export function createSystemInstruction(context: string, intentAnalysis: IntentAnalysis): string {
  const selectedTaskInstruction = intentSpecificTasks[intentAnalysis.intent as keyof typeof intentSpecificTasks] || intentSpecificTasks.general_profile;

  return `${overarching_guidelines}\n\n**CURRENT TASK:**\n${selectedTaskInstruction}\n\n**CONTEXT:**\n${context}`;
}

async function* streamLlmResponse(
  genAI: GoogleGenerativeAI,
  systemInstruction: string,
  messages: Array<ChatMessage>,
  signal?: AbortSignal
): AsyncGenerator<string> {
  const model = genAI.getGenerativeModel({
    model: "gemini-1.5-flash",
    systemInstruction: { role: "model", parts: [{ text: systemInstruction }] },
    generationConfig: { temperature: 0.15 }
  });

  const history = messages.slice(0, -1).map(msg => ({
    role: msg.role === "assistant" ? "model" : "user",
    parts: [{ text: msg.content }]
  }));

  const currentQuery = messages[messages.length - 1].content;

  const result = await model.generateContentStream({
    contents: [...history, { role: "user", parts: [{ text: currentQuery }] }],
    generationConfig: { temperature: 0.15 },
  }, { signal });

  for await (const chunk of result.stream) {
    yield chunk.text();
  }
}

export async function* runRagPipeline(
  query: string,
  messages: Array<ChatMessage>,
  embeddingService: EmbeddingService,
  queryEmbeddingCache: QueryEmbeddingCache,
  intentCache: IntentCache,
  genAI: GoogleGenerativeAI,
  supabase: SupabaseClient,
  signal?: AbortSignal
): AsyncGenerator<string> {
  // Simple alias normalization for common nicknames
  const normalizedQuery = query.replace(/\bMax\b/gi, "Maksym");
  if (normalizedQuery !== query) {
    console.log(`🔄 Query normalized: "${query}" -> "${normalizedQuery}"`);
  }

  const intentAnalysis = detectIntent(normalizedQuery, intentCache);
  console.log("🎯 Intent (pipeline):", intentAnalysis.intent, "Tech:", intentAnalysis.detectedTechnology);

  const k = intentAnalysis.needsComprehensiveProfile ? 20 : 15;

  const retrievedDocs = await retrieveDocuments(normalizedQuery, intentAnalysis, embeddingService, supabase, k);

  const context = formatContext(retrievedDocs);
  const systemInstruction = createSystemInstruction(context, intentAnalysis);

  let fullResponse = '';
  const responseGenerator = queryEmbeddingCache.getOrGenerateResponse(
    normalizedQuery,
    (text) => embeddingService.generateEmbedding(text),
    (msgs) => streamLlmResponse(genAI, systemInstruction, msgs, signal),
    messages
  );

  for await (const chunk of responseGenerator) {
    fullResponse += chunk;
    yield chunk;
  }
  console.log("✅ RAG Pipeline finished");
}
