
import { RAGRetriever } from "./ragRetriever";

// Global RAG instance that persists across requests
let ragRetriever: RAGRetriever | null = null;
let ragInitialized = false;
let ragInitializing = false;
let initializationPromise: Promise<RAGRetriever | null> | null = null;

// Initialize RAG on server startup
export async function initializeRAG(): Promise<RAGRetriever | null> {
    // If already initialized, return the existing instance
    if (ragInitialized && ragRetriever) {
        return ragRetriever;
    }

    // If currently initializing, return the existing promise
    if (ragInitializing && initializationPromise) {
        return await initializationPromise;
    }

    // Start new initialization
    ragInitializing = true;
    console.log("🚀 Starting RAG system initialization...");

    initializationPromise = (async (): Promise<RAGRetriever | null> => {
        try {
            ragRetriever = new RAGRetriever();
            await ragRetriever.initialize();
            ragInitialized = true;
            ragInitializing = false;
            console.log("✅ RAG system initialized successfully and ready for requests");
            return ragRetriever;
        } catch (error: unknown) {
            ragInitializing = false;
            ragInitialized = false;
            initializationPromise = null;
            console.error("❌ Failed to initialize RAG system:", error);
            return null;
        }
    })();

    return await initializationPromise;
}

// Get the current RAG instance
export function getRAGRetriever(): RAGRetriever | null {
    return ragRetriever;
}

// Check if RAG is initialized
export function isRAGInitialized(): boolean {
    return ragInitialized;
}

// Check if RAG is currently initializing
export function isRAGInitializing(): boolean {
    return ragInitializing;
}

// Wait for RAG initialization to complete
export async function waitForRAGInitialization(maxWaitTime = 30000): Promise<RAGRetriever | null> {
    if (ragInitialized && ragRetriever) {
        return ragRetriever;
    }

    if (ragInitializing && initializationPromise) {
        return await initializationPromise;
    }

    // Start initialization if not already started
    return await initializeRAG();
}
