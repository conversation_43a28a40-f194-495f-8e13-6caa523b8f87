import { supabase } from "@/integrations/supabase/client";

export interface VectorDocument {
  content: string;
  metadata: Record<string, unknown>;
  embedding?: number[];
}

export interface SearchResult {
  document: VectorDocument;
  score: number;
}

const SIMILARITY_THRESHOLD = parseFloat(import.meta.env.VITE_SIMILARITY_THRESHOLD || "0.1");

export class VectorStore {
  private isInitialized = false;

  async initialize(): Promise<void> {
    this.isInitialized = true;
    console.log("VectorStore initialized with Supabase pgvector backend");
  }

  async hasLoadedPrecomputedEmbeddings(): Promise<boolean> {
    try {
      const { data, error } = await supabase
        .from('document_embeddings')
        .select('id')
        .limit(1);

      if (error) {
        console.error("Error checking precomputed embeddings:", error);
        return false;
      }

      const hasEmbeddings = (data?.length || 0) > 0;
      console.log(`[VectorStore] Has embeddings: ${hasEmbeddings}`);
      return hasEmbeddings;
    } catch (error) {
      console.error("Error checking precomputed embeddings:", error);
      return false;
    }
  }

  async getLoadedDocumentCount(): Promise<number> {
    try {
      const { count, error } = await supabase
        .from('document_embeddings')
        .select('*', { count: 'exact', head: true });

      if (error) {
        console.error("Error getting document count:", error);
        return 0;
      }

      console.log(`[VectorStore] Document count: ${count || 0}`);
      return count || 0;
    } catch (error) {
      console.error("Error getting document count:", error);
      return 0;
    }
  }

  async addDocument(document: VectorDocument): Promise<void> {
    if (!document.embedding) {
      throw new Error("Document must have embedding already generated");
    }

    const embeddingVector = `[${document.embedding.join(',')}]`;

    const { error } = await supabase
      .from('document_embeddings')
      .insert({
        document_id: `doc_${Date.now()}_${Math.random()}`,
        content: document.content,
        metadata: document.metadata,
        embedding: embeddingVector
      });

    if (error) {
      console.error("Error adding document:", error);
      throw error;
    }
  }

  async addDocuments(documents: VectorDocument[]): Promise<void> {
    console.log(`Adding ${documents.length} documents to vector store...`);

    const embeddings = documents.map((doc, index) => {
      if (!doc.embedding) {
        throw new Error(`Document at index ${index} must have embedding already generated`);
      }

      return {
        document_id: `doc_${Date.now()}_${Math.random()}_${index}`,
        content: doc.content,
        metadata: doc.metadata,
        embedding: `[${doc.embedding.join(',')}]`
      };
    });

    const { error } = await supabase
      .from('document_embeddings')
      .insert(embeddings);

    if (error) {
      console.error("Error adding documents batch:", error);
      throw error;
    }

    console.log(`✅ Added ${documents.length} documents to vector store`);
  }

  async search(queryEmbedding: number[], k: number = 5): Promise<SearchResult[]> {
    try {
      console.log(`[VectorStore] Performing vector similarity search with k=${k}...`);
      console.log(`[VectorStore] Query embedding dimensions: ${queryEmbedding.length}`);

      const queryVector = `[${queryEmbedding.join(',')}]`;

      const { data, error } = await supabase.rpc('search_similar_documents', {
        query_embedding: queryVector,
        similarity_threshold: SIMILARITY_THRESHOLD,
        match_limit: k
      });

      if (error) {
        console.error("[VectorStore] Error in server-side vector search:", error);
        return await this.fallbackSearch(k);
      }

      if (!data || data.length === 0) {
        console.log("[VectorStore] No similar documents found with vector search, trying fallback");
        return await this.fallbackSearch(k);
      }

      const results: SearchResult[] = data.map(row => ({
        document: {
          content: row.content,
          metadata: row.metadata as Record<string, unknown>
        },
        score: row.similarity
      }));

      console.log(`[VectorStore] Found ${results.length} similar documents. Scores:`,
        results.map(r => r.score.toFixed(3)));

      // Log sample content for debugging
      if (results.length > 0) {
        console.log(`[VectorStore] Sample content:`, results[0].document.content.substring(0, 100));
      }

      return results;
    } catch (error) {
      console.error("[VectorStore] Error in vector search:", error);
      return await this.fallbackSearch(k);
    }
  }

  private async fallbackSearch(k: number): Promise<SearchResult[]> {
    console.log("[VectorStore] Using fallback search without similarity scoring");

    try {
      const { data, error } = await supabase
        .from('document_embeddings')
        .select('content, metadata, document_id')
        .limit(k);

      if (error) {
        console.error("[VectorStore] Error in fallback search:", error);
        return [];
      }

      console.log(`[VectorStore] Fallback search returned ${data?.length || 0} documents`);

      return data?.map(row => ({
        document: {
          content: row.content,
          metadata: row.metadata as Record<string, unknown>
        },
        score: 0.5 // Default similarity for fallback
      })) || [];
    } catch (error) {
      console.error("[VectorStore] Fallback search failed:", error);
      return [];
    }
  }

  async clear(): Promise<void> {
    const { error } = await supabase
      .from('document_embeddings')
      .delete()
      .neq('id', '00000000-0000-0000-0000-000000000000');

    if (error) {
      console.error("Error clearing vector store:", error);
      throw error;
    }
  }

  async getDocumentCount(): Promise<number> {
    return await this.getLoadedDocumentCount();
  }

  // Add method to check what's actually in the database
  async debugDocumentContent(limit: number = 5): Promise<void> {
    try {
      const { data, error } = await supabase
        .from('document_embeddings')
        .select('document_id, content, metadata')
        .limit(limit);

      if (error) {
        console.error("[VectorStore] Debug query error:", error);
        return;
      }

      console.log(`[VectorStore] Debug: Found ${data?.length || 0} documents in database`);
      data?.forEach((doc, idx) => {
        console.log(`[VectorStore] Doc ${idx + 1}:`, {
          id: doc.document_id,
          contentLength: doc.content.length,
          contentPreview: doc.content.substring(0, 100),
          metadata: doc.metadata
        });
      });
    } catch (error) {
      console.error("[VectorStore] Debug query failed:", error);
    }
  }
}
