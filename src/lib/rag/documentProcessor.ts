import { RAGDocument } from "./types";

export class DocumentProcessor {
  constructor() {
    console.log("DocumentProcessor initialized");
  }

  private shouldSkipFile(filename: string): boolean {
    const skipPatterns = [
      '.zone.identifier',
      '.ds_store',
      'thumbs.db',
      'desktop.ini',
      '.tmp',
      '.temp',
      '.cache',
      '.lock'
    ];

    const lowerFilename = filename.toLowerCase();
    return skipPatterns.some(pattern => lowerFilename.includes(pattern));
  }

  async getDirectoryList(): Promise<string[]> {
    // Server-side directory listing
    if (typeof window !== 'undefined') {
      console.log("Directory listing skipped in browser environment");
      return [];
    }

    try {
      const fs = (await import('fs-extra')).default;
      const path = await import('path');

      const codeBasePath = path.resolve("public/documents/code");
      console.log(`Code base path resolved to: ${codeBasePath}`);

      if (!(await fs.pathExists(codeBasePath))) {
        console.log(`Code base path does not exist: ${codeBasePath}`);
        return [];
      }

      const directories: string[] = [];
      const itemsInCodeBase = await fs.readdir(codeBasePath);

      for (const itemName of itemsInCodeBase) {
        if (this.shouldSkipFile(itemName)) {
          continue;
        }

        const itemPath = path.join(codeBasePath, itemName);
        const stat = await fs.stat(itemPath);

        if (stat.isDirectory()) {
          directories.push(itemName);
        } else {
          // Add "root" for top-level files
          if (!directories.includes("_root_files")) {
            directories.push("_root_files");
          }
        }
      }

      return directories.sort();
    } catch (error) {
      console.error("Error getting directory list:", error);
      return [];
    }
  }

  async getFilePathsInDirectory(directoryName: string): Promise<string[]> {
    if (typeof window !== 'undefined') return [];
    try {
      const fs = (await import('fs-extra')).default;
      const path = await import('path');
      const codeBasePath = path.resolve("public/documents/code");
      const filePaths: string[] = [];

      if (directoryName === "_root_files") {
        const itemsInCodeBase = await fs.readdir(codeBasePath);
        for (const itemName of itemsInCodeBase) {
          if (this.shouldSkipFile(itemName)) continue;
          const itemPath = path.join(codeBasePath, itemName);
          const stat = await fs.stat(itemPath);
          if (!stat.isDirectory() && (this.isCodeFile(itemName) || itemName.toLowerCase().endsWith('.json') || itemName.toLowerCase().endsWith('.md'))) {
            filePaths.push(itemPath);
          }
        }
      } else {
        const projectPath = path.join(codeBasePath, directoryName);
        if (!(await fs.pathExists(projectPath))) {
          console.log(`Project directory does not exist: ${projectPath}`);
          return [];
        }
        const projectFiles = await this.recursivelyGetFiles(projectPath, fs, path);
        for (const filePathInProject of projectFiles) {
          const filename = path.basename(filePathInProject);
          if (this.shouldSkipFile(filename)) continue;
          if (filename.toLowerCase() === "readme.md" || this.isCodeFile(filename)) {
            filePaths.push(filePathInProject);
          } else {
            console.log(`    Skipping non-code/non-README file in project ${directoryName} (path gathering): ${path.relative(projectPath, filePathInProject)}`);
          }
        }
      }
      console.log(`    Found ${filePaths.length} processable file paths in ${directoryName}`);
      return filePaths;
    } catch (error) {
      console.error(`Error getting file paths from directory ${directoryName}:`, error);
      return [];
    }
  }

  async loadSingleDocumentByPath(filePath: string, directoryContextName: string): Promise<RAGDocument | null> {
    if (typeof window !== 'undefined') return null;
    try {
      const fs = (await import('fs-extra')).default;
      const path = await import('path');

      const filename = path.basename(filePath);
      const content = await fs.readFile(filePath, "utf-8");
      let docType: "personal_info" | "code" | "project_readme" = "code";
      let metadata: RAGDocument['metadata'] = { type: "code", filename };
      let idPrefix: string;

      if (directoryContextName === "_root_files") {
        docType = filename.toLowerCase().endsWith('.json') ? "personal_info" : "code";
        idPrefix = `${docType}_`;
        metadata = { type: docType, filename };
      } else { // It's a project directory
        const codeBasePath = path.resolve("public/documents/code");
        const projectPath = path.join(codeBasePath, directoryContextName);
        const relativePath = path.relative(projectPath, filePath);

        if (filename.toLowerCase() === "readme.md") {
          docType = "project_readme";
          idPrefix = `project_readme_${directoryContextName}_`;
        } else {
          docType = "code";
          idPrefix = `code_${directoryContextName}_`;
        }
        metadata = {
          type: docType,
          filename: filename,
          project: directoryContextName,
          relative_path: relativePath,
        };
        idPrefix += relativePath.replace(/[/\\]|\./g, "_"); // use full relative path for ID uniqueness
      }

      // Add technology detection
      const detectedTechnologies = this.detectTechnologies(filename, directoryContextName, content);
      if (detectedTechnologies.length > 0) {
        metadata.technologies = detectedTechnologies;
        if (detectedTechnologies.length === 1) {
          metadata.technology = detectedTechnologies[0];
        }
      }

      const id = (directoryContextName === "_root_files")
        ? `${idPrefix}${filename.replace(/\./g, "_")}`
        : `${idPrefix}`; // idPrefix for projects already includes relative path structure

      console.log(`    Loaded ${docType}: ${filePath} (${content.length} chars)`);
      return {
        id: id,
        content,
        metadata,
      };

    } catch (error) {
      console.error(`Error loading document ${filePath}:`, error);
      return null;
    }
  }

  /**
   * Loads all documents from a given directory into memory as RAGDocument objects.
   * This method is not memory efficient for large directories, as it loads all file contents at once.
   * For large-scale or production use, prefer getFilePathsInDirectory and loadSingleDocumentByPath for streaming/batch processing.
   * @param directoryName The name of the directory to load documents from.
   * @returns Promise<RAGDocument[]> Array of loaded RAGDocument objects.
   */
  async loadDocumentsFromDirectory(directoryName: string): Promise<RAGDocument[]> {
    if (typeof window !== 'undefined') return [];
    console.warn("Using loadDocumentsFromDirectory, consider iterative approach for large directories.");
    const filePaths = await this.getFilePathsInDirectory(directoryName);
    const documents: RAGDocument[] = [];
    for (const filePath of filePaths) {
      const doc = await this.loadSingleDocumentByPath(filePath, directoryName);
      if (doc) {
        documents.push(doc);
      }
    }
    return documents;
  }

  async loadDocuments(): Promise<RAGDocument[]> {
    // ... (this method loads ALL documents from ALL projects, even more memory intensive)
    // ... (Skipping full paste for brevity)
    console.warn("Using loadDocuments, this loads ALL documents and can be very memory intensive.");
    const directories = await this.getDirectoryList();
    const allDocuments: RAGDocument[] = [];
    for (const dir of directories) {
      const docsInDir = await this.loadDocumentsFromDirectory(dir); // Uses the less efficient one
      allDocuments.push(...docsInDir);
    }
    return allDocuments;
  }

  private async recursivelyGetFiles(dir: string, fs: typeof import('fs-extra'), path: typeof import('path')): Promise<string[]> {
    const dirents = await fs.readdir(dir, { withFileTypes: true });
    const files = await Promise.all(
      dirents.map(async (dirent: import('fs-extra').Dirent) => { // Added async here for fs.statSync potentially being slow
        const res = path.resolve(dir, dirent.name);
        if (this.shouldSkipFile(dirent.name)) return [];
        if (
          dirent.isDirectory() &&
          ![".git", "node_modules", ".venv", "__pycache__", ".ds_store", "build", "dist",
            "target", ".idea", ".vscode", "img", "images", "assets", "static", ".next",
            "coverage", ".nyc_output", "logs", "tmp", "temp",
          ].includes(dirent.name.toLowerCase())
        ) {
          return this.recursivelyGetFiles(res, fs, path);
        }
        // Ensure it's a file before returning (and exists, though readdir should ensure it)
        // Check if `res` is a file path and not a directory that was skipped
        if (dirent.isFile()) {
          return res;
        }
        // If it's a directory that got skipped by the list above, it won't be returned as a file.
        // If it was a directory that was processed recursively, its files will be returned.
        // If it's something else we don't handle (symlink, etc.), it will be filtered out.
        return []; // Return empty array for things we don't want / can't process
      }),
    );
    // Flatten and filter: ensure items are actual file paths
    // The previous filter with fs.existsSync and fs.statSync was good, let's keep it.
    const flattenedFiles = Array.prototype.concat(...files);
    const validFiles = [];
    for (const file of flattenedFiles) {
      if (file && (await fs.pathExists(file)) && (await fs.stat(file)).isFile()) {
        validFiles.push(file);
      }
    }
    return validFiles;
  }

  isCodeFile(filename: string): boolean {
    const codeExtensions = [
      ".py",
      ".js",
      ".ts",
      ".tsx",
      ".jsx",
      ".go",
      ".sql",
      ".yaml",
      ".yml",
      ".sh",
      ".dockerfile",
      "dockerfile",
      ".java",
      ".cs",
      ".cpp",
      ".c",
      ".h",
      ".hpp",
      ".rb",
      ".php",
      ".swift",
      ".kt",
      ".kts",
      ".rs",
      ".r",
      ".scala",
      ".clj",
      ".pl",
      ".lua",
      ".dart",
      ".vue",
      ".svelte",
      ".html",
      ".css",
      ".scss",
      ".less",
      ".json",
      ".xml",
      ".toml",
      ".ini",
      ".cfg",
      ".conf",
    ];
    const lowerFilename = filename.toLowerCase();

    if (codeExtensions.includes(lowerFilename)) {
      return true;
    }
    return codeExtensions.some((ext) => lowerFilename.endsWith(ext));
  }

  chunkDocument(
    document: RAGDocument,
    chunkSize = 20000, // Increased to preserve complete content
    overlap = 512,
  ): RAGDocument[] {
    const chunks: RAGDocument[] = [];
    const content = document.content;

    if (!content || content.trim().length === 0) {
      console.warn(`Document ${document.id} has no content to chunk. Skipping.`);
      return [];
    }

    // Improved: Smarter Markdown/README chunking
    if (
      document.metadata.filename.toLowerCase().endsWith(".md") ||
      document.metadata.type === "project_readme"
    ) {
      // Simple approach: split by double newlines (paragraphs) and group into chunks
      const paragraphs = content.split(/\n\s*\n/).filter(p => p.trim().length > 0);

      let currentChunk = "";
      let chunkIndex = 0;

      for (let i = 0; i < paragraphs.length; i++) {
        const paragraph = paragraphs[i].trim();
        const potentialChunk = currentChunk + (currentChunk ? "\n\n" : "") + paragraph;

        // If adding this paragraph would exceed chunk size, save current chunk
        if (potentialChunk.length > chunkSize && currentChunk.length > 0) {
          chunks.push({
            id: `${document.id}_chunk_${chunkIndex}`,
            content: currentChunk.trim(),
            metadata: {
              ...document.metadata,
              parent_id: document.id,
              chunk_index: chunkIndex,
              original_length: content.length,
            },
          });
          chunkIndex++;
          currentChunk = paragraph; // Start new chunk with current paragraph
        } else {
          currentChunk = potentialChunk;
        }
      }

      // Add final chunk if there's remaining content
      if (currentChunk.trim().length > 0) {
        chunks.push({
          id: `${document.id}_chunk_${chunkIndex}`,
          content: currentChunk.trim(),
          metadata: {
            ...document.metadata,
            parent_id: document.id,
            chunk_index: chunkIndex,
            original_length: content.length,
          },
        });
      }

      if (chunks.length > 0) return chunks;
    }

    // Improved: Code-aware chunking
    const fileExt = (document.metadata.filename || "").toLowerCase();

    // Special handling for SQL files - split by statements
    if (fileExt.endsWith(".sql")) {
      const statements = content.split(';').filter(s => s.trim().length > 0);

      for (let i = 0; i < statements.length; i++) {
        const statement = statements[i].trim();
        if (statement.length === 0) continue;

        chunks.push({
          id: `${document.id}_stmt_${i}`,
          content: statement,
          metadata: {
            ...document.metadata,
            parent_id: document.id,
            chunk_index: i,
            is_sql_statement: true,
            original_length: content.length,
          },
        });
      }

      if (chunks.length > 0) return chunks;
    }

    // For other code files, use line-by-line chunking
    if (this.isCodeFile(fileExt)) {
      const lines = content.split("\n");
      let currentChunk = "";
      let chunkIndex = 0;
      let startLine = 0;

      for (let i = 0; i < lines.length; i++) {
        const line = lines[i];
        const potentialChunk = currentChunk + (currentChunk ? "\n" : "") + line;

        // Check if we should split here
        const shouldSplit = potentialChunk.length > chunkSize &&
          (i === lines.length - 1 || // Last line
            /^\s*(def |function |class |async |export |public |private |protected |static |describe\(|it\(|\/\*|\/\/|#)/.test(lines[i + 1] || "")); // Next line starts a new block

        if (shouldSplit && currentChunk.length > 0) {
          chunks.push({
            id: `${document.id}_chunk_${chunkIndex}`,
            content: currentChunk.trim(),
            metadata: {
              ...document.metadata,
              parent_id: document.id,
              chunk_index: chunkIndex,
              original_length: content.length,
              start_line: startLine,
              end_line: i,
            },
          });
          chunkIndex++;
          currentChunk = line;
          startLine = i;
        } else {
          currentChunk = potentialChunk;
        }
      }

      // Add final chunk
      if (currentChunk.trim().length > 0) {
        chunks.push({
          id: `${document.id}_chunk_${chunkIndex}`,
          content: currentChunk.trim(),
          metadata: {
            ...document.metadata,
            parent_id: document.id,
            chunk_index: chunkIndex,
            original_length: content.length,
            start_line: startLine,
            end_line: lines.length - 1,
          },
        });
      }

      if (chunks.length > 0) return chunks;
    }

    // Default chunking - sliding window approach
    let start = 0;
    let chunkIndex = 0;

    while (start < content.length) {
      let end = Math.min(start + chunkSize, content.length);

      // Try to find a good break point if not at end of content
      if (end < content.length) {
        let breakPoint = -1;

        // For text files, prefer sentence endings
        if (!this.isCodeFile(fileExt)) {
          for (const breakChar of ['. ', '.\n', '! ', '!\n', '? ', '?\n', '\n\n']) {
            const pos = content.lastIndexOf(breakChar, end);
            if (pos > start + chunkSize * 0.5) { // Don't break too early
              breakPoint = pos + breakChar.length;
              break;
            }
          }
        }

        // Fallback to newline break
        if (breakPoint === -1) {
          const newlinePos = content.lastIndexOf('\n', end);
          if (newlinePos > start + chunkSize * 0.3) {
            breakPoint = newlinePos + 1;
          }
        }

        if (breakPoint !== -1) {
          end = breakPoint;
        }
      }

      const chunkText = content.slice(start, end).trim();
      if (chunkText.length > 0) {
        chunks.push({
          id: `${document.id}_chunk_${chunkIndex}`,
          content: chunkText,
          metadata: {
            ...document.metadata,
            parent_id: document.id,
            chunk_index: chunkIndex,
            original_length: content.length,
            start_position: start,
            end_position: end,
          },
        });
        chunkIndex++;
      }

      // Move start position with overlap
      start = Math.max(end - overlap, start + 1);
    }

    return chunks;
  }

  private detectTechnologies(filename: string, projectName: string, content: string): string[] {
    const technologies: string[] = [];
    const lowerFilename = filename.toLowerCase();
    const lowerProjectName = projectName.toLowerCase();
    const lowerContent = content.toLowerCase();

    // Detect based on file extensions
    if (lowerFilename.endsWith('.go') || lowerFilename.endsWith('.mod') || lowerFilename.endsWith('.sum')) {
      technologies.push('golang');
    }
    if (lowerFilename.endsWith('.py') || lowerFilename.endsWith('.ipynb')) {
      technologies.push('python');
    }
    if (lowerFilename.endsWith('.js') || lowerFilename.endsWith('.ts') || lowerFilename.endsWith('.jsx') || lowerFilename.endsWith('.tsx')) {
      technologies.push('javascript');
    }
    if (lowerFilename.endsWith('.sql')) {
      technologies.push('sql');
    }
    if (lowerFilename.endsWith('.dockerfile') || lowerFilename === 'dockerfile') {
      technologies.push('docker');
    }
    if (lowerFilename.endsWith('.yaml') || lowerFilename.endsWith('.yml')) {
      technologies.push('yaml');
    }
    if (lowerFilename.endsWith('.json')) {
      technologies.push('json');
    }

    // Detect based on project names
    if (lowerProjectName.includes('gengo') || lowerProjectName.includes('go')) {
      technologies.push('golang');
    }
    if (lowerProjectName.includes('python') || lowerProjectName.includes('ai') || lowerProjectName.includes('ml')) {
      technologies.push('python');
    }
    if (lowerProjectName.includes('kafka') || lowerProjectName.includes('streaming') || lowerProjectName.includes('pipeline')) {
      technologies.push('streaming');
    }
    if (lowerProjectName.includes('react') || lowerProjectName.includes('node') || lowerProjectName.includes('js')) {
      technologies.push('javascript');
    }

    // Detect based on content keywords
    if (lowerContent.includes('package main') || lowerContent.includes('import "') || lowerContent.includes('func ') || lowerContent.includes('goroutine') || lowerContent.includes('channel')) {
      technologies.push('golang');
    }
    if (lowerContent.includes('import pandas') || lowerContent.includes('import numpy') || lowerContent.includes('def ') || lowerContent.includes('class ') || lowerContent.includes('from ')) {
      technologies.push('python');
    }
    if (lowerContent.includes('function ') || lowerContent.includes('const ') || lowerContent.includes('let ') || lowerContent.includes('var ') || lowerContent.includes('import ') || lowerContent.includes('export ')) {
      technologies.push('javascript');
    }
    if (lowerContent.includes('select ') || lowerContent.includes('from ') || lowerContent.includes('where ') || lowerContent.includes('join ') || lowerContent.includes('create table')) {
      technologies.push('sql');
    }
    if (lowerContent.includes('kafka') || lowerContent.includes('producer') || lowerContent.includes('consumer') || lowerContent.includes('topic')) {
      technologies.push('streaming');
    }

    // Remove duplicates and return
    return [...new Set(technologies)];
  }
}