
# Gemini Project Configuration

This file provides project-specific context and instructions for the Gemini AI assistant.

## Project Overview

This is a React-based web application built with Vite and TypeScript. It serves as a portfolio and interactive resume for <PERSON><PERSON><PERSON>. The application features a chat interface that uses a Retrieval-Augmented Generation (RAG) pipeline to answer questions about <PERSON><PERSON><PERSON>'s skills and experience.

### Core Technologies

*   **Frontend:** React, TypeScript, Vite
*   **UI Components:** Shadcn/UI
*   **Styling:** Tailwind CSS
*   **Routing:** `react-router-dom`
*   **State Management:** `@tanstack/react-query` (for server state), React Hooks (for local state)
*   **Backend:** Supabase (for database and vector store)
*   **AI/ML:** `@google/generative-ai`, `@huggingface/transformers`

## Coding Conventions

*   **Components:** Use functional components with React Hooks.
*   **Styling:** Use Tailwind CSS classes directly in the JSX. Do not write separate CSS files unless absolutely necessary.
*   **File Structure:**
    *   Pages go in `src/pages`.
    *   Reusable components go in `src/components`.
    *   UI-specific components (e.g., buttons, cards) go in `src/components/ui`.
    *   Layout components (e.g., `PageLayout`, `Navigation`) go in `src/components/layout`.
    *   API-related logic goes in `src/api`.
    *   Core application logic and utilities go in `src/lib`.
*   **Path Aliases:** Use the `@/*` alias for imports from the `src` directory (e.g., `import { Button } from '@/components/ui/button'`).
*   **Linting:** Adhere to the ESLint rules defined in `eslint.config.js`.

## Architectural Patterns

*   **Component-Based Architecture:** The application is built around reusable components.
*   **RAG Pipeline:** The chat functionality is powered by a RAG pipeline defined in `src/lib/rag/pipeline.ts`. This pipeline is responsible for:
    1.  **Intent Detection:** Determining the user's intent (e.g., asking about a specific project, a technical skill, or a general profile overview).
    2.  **Document Retrieval:** Retrieving relevant documents from a Supabase vector store based on the user's query and intent.
    3.  **Response Generation:** Using Google's Generative AI to generate a response based on the retrieved documents.
*   **Supabase Migrations:** Supabase is used for the backend, but the Supabase CLI is not used for migrations. All database schema changes are applied manually through the Supabase SQL editor.

## How to Run the Project

*   **Package Manager:** This project uses `pnpm`. All package-related commands (`install`, `add`, `remove`, `run`) must be executed with `pnpm`.
*   **Install dependencies:** `pnpm install`
*   **Run the development server:** `pnpm run dev`
*   **Build for production:** `pnpm run build`

## How to Interact with Me

When you ask me to make changes to the code, I will follow the conventions outlined in this file. For example, if you ask me to create a new component, I will create it in the appropriate directory and use functional component syntax. If you ask me to add a new feature, I will do my best to follow the existing architectural patterns.
