
networks:
  app_network:
    driver: bridge

services:
  kafka-job:
    build:
      context: .
    volumes:
      - /home/<USER>/code/Log-Generation/logs:/app/logs:ro
    depends_on:
      kafka:
        condition: service_healthy
      postgres:
        condition: service_healthy
    networks:
      - app_network
    command: ./start.sh

  postgres:
    image: timescale/timescaledb:latest-pg17
    environment:
      POSTGRES_USER: user
      POSTGRES_PASSWORD: password
      POSTGRES_DB: logs_db
    ports:
      - "5432:5432"
    volumes:
      - postgres_timescale_data:/var/lib/postgresql/data
      # - /home/<USER>/streaming_pipeline/postgresql.conf:var/lib/postgresql/data/postgresql.conf
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U user -d logs_db"]
      interval: 1s
      timeout: 5s
      retries: 5
    networks:
      - app_network

  zookeeper:
    image: confluentinc/cp-zookeeper:7.2.1
    environment:
      ZOOKEEPER_CLIENT_PORT: 2181
      ZOOKEEPER_TICK_TIME: 2000
    ports:
      - "2181:2181"
    healthcheck:
      test: ["CMD-SHELL", "echo stat | nc localhost 2181"]
      interval: 5s
      timeout: 5s
      retries: 5
    networks:
      - app_network

  kafka:
    image: confluentinc/cp-kafka:7.2.1
    depends_on:
      zookeeper:
        condition: service_healthy
    environment:
      KAFKA_BROKER_ID: 1
      KAFKA_ZOOKEEPER_CONNECT: zookeeper:2181
      KAFKA_ADVERTISED_LISTENERS: PLAINTEXT://kafka:9092
      KAFKA_OFFSETS_TOPIC_REPLICATION_FACTOR: 1
    ports:
      - "9092:9092"
    healthcheck:
      test: ["CMD-SHELL", "kafka-topics --list --bootstrap-server localhost:9092"]
      interval: 5s
      timeout: 5s
      retries: 5
    networks:
      - app_network


#  grafana:
#    image: grafana/grafana:latest
#    ports:
#      - "3000:3000"
#    volumes:
#      - grafana_data:/var/lib/grafana
#    networks:
#      - app_network

  # flink-jobmanager:
  #   image: flink:latest
  #   ports:
  #     - "8081:8081"
  #   environment:
  #     - JOB_MANAGER_RPC_ADDRESS=flink-jobmanager
  #   command: jobmanager
  #   networks:
  #     - app_network
  #
  # flink-taskmanager:
  #   image: flink:latest
  #   depends_on:
  #     - flink-jobmanager
  #   environment:
  #     - JOB_MANAGER_RPC_ADDRESS=flink-jobmanager
  #   command: taskmanager
  #   networks:
  #     - app_network

volumes:
  postgres_timescale_data:
  grafana_data:
