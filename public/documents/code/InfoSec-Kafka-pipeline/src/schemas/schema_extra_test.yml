logs_schema_extra:
  id: "SERIAL PRIMARY KEY"
  timestamp: "TIMESTAMP NOT NULL"
  log_id: "TEXT NOT NULL"
  event_type: "TEXT NOT NULL"
  user_id: "TEXT"
  username: "TEXT"
  ip_address: "TEXT"
  country: "TEXT"
  region: "TEXT"
  city: "TEXT"
  coordinates: "TEXT"
  os: "TEXT"
  browser: "TEXT"
  device_type: "TEXT"
  action: "TEXT"
  status: "TEXT"
  session_id: "TEXT"
  request_id: "TEXT"
  trace_id: "TEXT"
  extra_column: "TEXT"  # Extra column for testing


