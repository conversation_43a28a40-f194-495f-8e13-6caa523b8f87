{"ea_localization_lead_role": "Localization Quality Analyst at Electronic Arts (EA) from June 2021 to June 2023, promoted to Localization Quality Lead from June 2023 to October 2024 in Madrid, Spain. As QA Analyst: Performed comprehensive linguistic testing of EA games including FIFA, EA FC and Sims 4.Executed functional testing to identify text truncation, UI overlap, character encoding issues, and cultural inconsistencies. Verified subtitle synchronization and audio localization quality. Collaborated with development teams to reproduce and document localization bugs using JIRA, ensuring proper context and severity classification. Conducted comparative analysis between source and localized content to maintain narrative coherence and brand consistency. As Team Lead: Managed a 6-person Localization QA team across multiple EA titles and regions. Established standardized testing protocols and linguistic validation processes for consistent quality across all supported languages. Coordinated with international QA teams in Vancouver, Austin, and Bucharest to align testing methodologies. Led training programs for new QA testers on EA's localization standards, cultural sensitivity guidelines, and platform-specific requirements. Implemented risk assessment frameworks to prioritize testing efforts based on market importance and linguistic complexity. Facilitated communication between localization vendors, internal development teams, and regional publishing teams to resolve complex linguistic and technical issues.", "ea_quantified_achievements": "At Electronic Arts, achieved 30% quality improvement and 20% productivity gain through data-driven KPIs. Reduced in-game text discrepancies by 20% through systematic SQL database validation. Achieved 15-20% processing time reduction through automated reporting workflows. Led 6-member team across 5+ departments and successfully shipped AAA titles meeting Microsoft and Sony certification standards.", "ea_transferable_skills": "Developed a strong foundation in skills directly applicable to data and operations roles. The experience in localization QA is fundamentally rooted in data quality, validation, and integrity, ensuring accuracy across complex systems. This translates to managing data pipelines and maintaining data quality standards. Leadership of the QA team provides proven technical leadership and team management capabilities. A demonstrated ability to optimize workflows and automate processes, evidenced by reducing bug resolution times, is central to operational efficiency. When combined with practical skills in Python for scripting and building CLIs, and SQL for data manipulation, this background provides a unique and powerful justification for a transition into a data engineering or operations role, where process optimization, data quality, and cross-functional leadership are paramount.", "appen_ai_data_role": "Language Analyst and AI Data Annotator at Appen from May 2019 to May 2023, working remotely. Contributed to ML model improvement through high-quality data annotation and analysis for major tech companies including Google, Meta, and Twitter. Focused on enhancing AI systems that serve millions of users worldwide.", "appen_quantified_achievements": "At Appen, achieved 25% improvement in user engagement metrics through systematic content analysis. Enhanced ML model performance for millions of users across major platforms. Worked directly with major clients including Google, Meta, and Twitter, contributing to their AI and machine learning initiatives.", "appen_technical_contributions": "Enhanced sentiment analysis accuracy for major search engines and social media platforms. Analyzed and annotated content across marketing, finance, and business domains. Developed expertise in data categorization methodologies for AI applications. Improved search result relevance and accuracy while ensuring data privacy compliance.", "appen_skills_developed": "Developed deep understanding of ML data pipelines, data quality assurance methodologies, and large-scale data processing workflows. Gained expertise in AI training data preparation, content analysis, and systematic quality control processes that directly apply to data engineering and machine learning operations.", "experience_keywords_summary": "A unique blend of team leadership in AAA gaming at Electronic Arts and hands-on AI/ML data experience at Appen for clients like Google and Meta. Proven expertise in data quality assurance, process optimization, and team management, underscored by strong skills in SQL for database validation and Python for automation. Drove significant, quantifiable improvements including a 30% increase in quality and a 20% gain in productivity, while leading teams to successfully ship major titles and enhance ML models serving millions of users. This dual background provides a comprehensive perspective on managing the full data lifecycle in high-stakes, quality-driven environments."}