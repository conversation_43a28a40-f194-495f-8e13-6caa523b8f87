# Vendor Spend Analysis & Optimization Project

## 1. Project Objective

This project simulates the role of an acquisition analyst tasked with evaluating the vendor spend of a recently acquired company. The primary goals were to:

1.  Categorize vendor spend.
2.  Summarize each vendor's role.
3.  Provide strategic recommendations to reduce or optimize costs, aligning with the acquiring company's remote-first, lean operational model.

## 2. Context & Given Materials

- **Acquiring Company Profile:** Operates in the software space with a remote-first, lean operational model.
- **Acquired Business Profile:** Inherited a pre-existing vendor setup, including tools, services, and office-related expenses.
- **Files Provided:**
  - `Vendor Analysis Assessment`: A spreadsheet listing ~400 vendors and their spend over the last 12 months (real data with modified names/costs).

## 3. Expected Deliverables

1.  **Completed Spreadsheet:**
    - For each vendor: Assigned Department, 1-line Description, Strategic Recommendation (Terminate, Consolidate, Optimize).
    - This was to be achieved primarily using AI tools, with quality control.
2.  **Top 3 Strategic Opportunities Tab:**
    - Outline the three highest-impact recommendations.
    - Each including a summary title, brief explanation, and estimated annual savings.
3.  **Methodology Tab/Document:**
    - Explanation of the approach, tools used, prompts created, and validation methods.

## 4. My Approach & Execution

My approach was a multi-phase, iterative process leveraging Google Sheets, Google Apps Script, and the Gemini API to automate data enrichment, analysis, and reporting. The core idea was to use AI for tasks requiring contextual understanding and summarization, and Apps Script for orchestration, calculation, and sheet manipulation.

### 4.1. Tools Used:

- **Google Sheets:** Central hub for data input, processing, and final reports.
- **Google Apps Script:** The backbone for automation, including:
  - Reading and writing data to Google Sheets.
  - Making controlled API calls to the Gemini API.
  - Implementing logic for data processing, calculations, and error handling.
  - Creating custom menus for ease of use.
- **Gemini API (Model: `gemini-2.0-flash`):**
  - Used for intelligent data processing:
    - Assigning departments to vendors.
    - Generating concise vendor descriptions.
    - Providing initial strategic recommendations (Terminate/Optimize).
    - Identifying consolidation opportunities within departments.
    - Refining titles and explanations for strategic reports.

### 4.2. Phase 1: Initial Vendor Data Enrichment (Automated - Pass 1)

- **Goal:** Assign a department, generate a description, and give an initial "Terminate" or "Optimize" recommendation for each vendor. "Consolidate" was deliberately excluded at this stage to gather full context first.
- **Process:** An Apps Script function (`analyzeAllVendors_Pass1`) iterated through each vendor, calling the Gemini API.
- **Key Prompt Snippet (`getVendorAnalysisFromGemini_Pass1`):**
  ```
  For the vendor named "${vendorName}", provide:
  1. Department: (Choose from: [${departmentListString}])
  2. 1-line Description: ...
  3. Strategic Recommendation: (Choose: Terminate or Optimize). Do NOT suggest 'Consolidate'...
  Format as JSON: {"department": ..., "description": ..., "suggestion_action": ..., "suggestion_reason": ...}
  ```
- **Refinements:** Implemented robust error handling for API calls (rate limits, safety blocks, malformed responses) and logic to skip already processed rows.

### 4.3. Phase 2: Consolidation Analysis (Automated - Pass 2)

- **Goal:** Identify vendors with overlapping functionalities within the same department that could be consolidated.
- **Process:** An Apps Script function (`analyzeConsolidationsByDepartment_Pass2`) grouped vendors by department and sent these groups to Gemini for consolidation analysis.
- **Key Prompt Snippet (`getConsolidationSuggestionForDepartmentFromGemini_Pass2`):**
  ```
  Reviewing vendors within the '${departmentName}' department...
  When recommending a vendor to keep, prioritize robust functionality suitable for a primary business tool... Avoid selecting extremely low-cost vendors as primary if more substantial alternatives exist...
  Vendors: ${vendorListString}
  Identify groups... recommend which to keep... state reason...
  Format as JSON: {"consolidations": [{"vendors_to_consolidate": [...], "recommended_vendor_to_keep": ..., "reason": ...}]}
  ```
- **Refinements:** Prompt was enhanced to guide AI on cost-priority for primary tools and to handle self-consolidation edge cases. Output updated "Suggestion" and "Consolidation Target/Notes" columns.

### 4.4. Phase 3: Strategic Opportunity Identification & Reporting

- **Goal:** Calculate overall financial impact and present the top 3 strategic cost-saving themes.
- **Process:** A dedicated Apps Script function (`runStrategicOpportunityAnalysis`) performed calculations and used Gemini for final text refinement.
  - **Departmental Impact Analysis:** Calculated "Current Spend," various "Estimated Savings" types (Termination, Consolidation, Optimization - assuming 10% for optimization), and "Estimated Future Spend" per department. This was output to a new "Departmental Cost Impact" sheet.
  - **Top 3 Strategic Opportunities Identified:**
    1.  **Eliminate Physical Office Infrastructure Costs:** Sum of terminated "Facilities" vendors.
    2.  **Streamline Software Stack via Consolidation:** Sum of all vendors marked "Consolidate."
    3.  **Optimize Spend on Essential Software & Services:** Sum of 10% of costs for "Optimize"/"Optimize (Primary)" vendors.
  - **AI Refinement of Top 3 Opportunities:**
    - **Key Prompt Snippet (`getStrategicRefinedTextFromGemini`):**
      ```
      Given strategic cost-saving opportunity: Theme: "${opportunity.type}", Title Suggestion: "${opportunity.title_raw}", Details: "${opportunity.details}", Savings: $${opportunity.savings}
      Generate: 1. "Opportunity Title": (concise, impactful) 2. "Brief Explanation": (1-2 sentences)
      Format as JSON: {"title": ..., "explanation": ...}
      ```
    - Results populated the "Top 3 Opportunities" sheet.

### 4.5. Quality Control & Validation

- **Iterative Prompt Engineering:** Prompts were continually refined based on AI output quality, ensuring clarity, desired formatting (JSON), and alignment with strategic goals.
- **Script Logic & Error Handling:** The Apps Script code was developed iteratively, with robust error handling for API interactions and data processing.
- **Manual Review & Business Logic:** AI outputs were critically reviewed (simulated via our collaborative process). For example, initial AI suggestions for consolidation sometimes favored very low-cost tools as primary; prompts were adjusted to incorporate better business logic regarding cost vs. capability. The obvious impact of terminating facilities costs was manually confirmed early on, validating the overall direction.
- **Data Consistency:** Ensured vendor names and financial data were handled consistently.
- **Assumption Transparency:** Key assumptions (e.g., 10% optimization savings, 0% cost increase on primary tools post-consolidation for initial estimate) were explicitly defined.

## 5. Key Outputs & Results

- **Fully Processed "Vendor Analysis Assessment" Sheet:** All vendors categorized with descriptions and strategic recommendations.
- **"Departmental Cost Impact" Sheet:** Detailed breakdown of current spend, potential savings by category, and projected future spend for each department.
- **"Top 3 Opportunities" Sheet:** Highlighting:
  1.  Elimination of Physical Office Infrastructure Costs
  2.  Streamlining Software Stack via Consolidation
  3.  Optimizing Spend on Essential Software & Services

## 6. Challenges & Learnings

- **AI Nuance:** Guiding the AI to understand business priorities (e.g., cost vs. functionality in consolidations) required careful prompt engineering and iteration.
- **Rate Limits & Error Handling:** Managing API rate limits (`Utilities.sleep()`) and building resilient error handling for API responses was crucial for processing a large vendor list.
- **Structured Data from AI:** Ensuring the AI consistently returned data in the specified JSON format was key for automated parsing. The `responseMimeType: "application/json"` parameter in API calls was helpful.
- **Balancing Automation and Manual Insight:** While AI greatly accelerated the process, human oversight and strategic thinking were essential to validate outputs and frame the final recommendations effectively. For instance, the significant impact of terminating facilities costs was readily apparent even before full automation, guiding the focus of the strategic opportunities.

## 7. Code

The Google Apps Script code used for this analysis can be found in the `CODE/` directory:

- [`vendor_analysis_script.gs`](./CODE/vendor_analysis_script.gs)

<!-- Example: [Link to Final Google Sheet (View Only)](your-shareable-link-here) -->
