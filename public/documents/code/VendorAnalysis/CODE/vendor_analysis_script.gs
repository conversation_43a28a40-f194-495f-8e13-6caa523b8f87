const API_KEY = PropertiesService.getScriptProperties().getProperty('GEMINI_API_KEY');
const GEMINI_API_ENDPOINT = `https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash-lite:generateContent?key=${API_KEY}`;


const VENDOR_SHEET_NAME = "Vendor Analysis Assessment"; 
const CONFIG_SHEET_NAME = "Config"; 

const VENDOR_NAME_COL = 1; // Column A
const DEPARTMENT_COL = 2;  // Column B (to be filled)
const COST_COL = 3;        // Column C
const DESCRIPTION_COL = 4; // Column D (to be filled)
const SUGGESTION_COL = 5;  // Column E (to be filled)

const CONSOLIDATION_NOTES_COL = 6;

const START_ROW = 2; 

const SA_OPTIMIZATION_SAVINGS_PERCENTAGE = 0.10;
const SA_DEPT_IMPACT_SHEET_NAME = "Departmental Cost Impact";
const SA_TOP_3_OPPS_SHEET_NAME = "Top 3 Opportunities";

// --- Helper function to get department list ---
function getDepartmentList() {
  try {
    const ss = SpreadsheetApp.getActiveSpreadsheet();
    const configSheet = ss.getSheetByName(CONFIG_SHEET_NAME);
    if (!configSheet) {
      SpreadsheetApp.getUi().alert(`Error: Config sheet "${CONFIG_SHEET_NAME}" not found.`);
      return null;
    }
    // Get all values from column A, filter out empty cells, and flatten the array
    const departments = configSheet.getRange("A1:A").getValues()
                                  .filter(row => row[0] !== "")
                                  .map(row => row[0]);
    if (departments.length === 0) {
        SpreadsheetApp.getUi().alert(`Error: No departments found in column A of "${CONFIG_SHEET_NAME}" sheet.`);
        return null;
    }
    return departments;
  } catch (e) {
    Logger.log(`Error in getDepartmentList: ${e}`);
    SpreadsheetApp.getUi().alert(`Error reading department list: ${e.message}`);
    return null;
  }
}
function getVendorAnalysisFromGemini_Pass1(vendorName, departmentListString) {
  // ... (prompt and payload definition remain the same) ...
  const prompt = `
You are an acquisition analyst. The acquiring company is a remote-first, lean software company.
You are evaluating vendor spend from an acquired business which had pre-acquisition setup including tools, services, and office-related expenses.
Your task is to assess where costs can be optimized or eliminated.

For the vendor named "${vendorName}", provide:
1.  Department: Choose the most appropriate department from this list: [${departmentListString}].
2.  1-line Description: A concise description of what the vendor likely does.
3.  Strategic Recommendation: Initially, choose one action: Terminate (vendor is no longer needed, e.g. irrelevant office expenses) or Optimize (useful vendor, potential to reduce cost/usage). Do NOT suggest 'Consolidate' in this initial pass. Provide a brief reason for your action.

Format your response strictly as a single JSON object like this example:
{
  "department": "SELECTED_DEPARTMENT",
  "description": "CONCISE_VENDOR_DESCRIPTION",
  "suggestion_action": "Terminate",
  "suggestion_reason": "BRIEF_REASON_FOR_ACTION"
}
Do not include any text before or after the JSON object.
`;

  const payload = {
    "contents": [{ "parts": [{ "text": prompt }] }],
    "generationConfig": {
      "temperature": 0.2,
      "topK": 1,
      "topP": 1,
      "maxOutputTokens": 300,
      "responseMimeType": "application/json"
    }
  };
  const options = {
    'method': 'post',
    'contentType': 'application/json',
    'payload': JSON.stringify(payload),
    'muteHttpExceptions': true
  };

  try {
    const response = UrlFetchApp.fetch(GEMINI_API_ENDPOINT, options);
    const responseCode = response.getResponseCode();
    const responseBody = response.getContentText();

    if (responseCode === 200) {
      const jsonResponse = JSON.parse(responseBody);

      // --- MODIFIED ERROR CHECKING ---
      if (!jsonResponse.candidates || jsonResponse.candidates.length === 0) {
        Logger.log(`No candidates returned from Gemini for ${vendorName} (Pass 1). Full response: ${responseBody}`);
        // Check for prompt feedback if available
        if (jsonResponse.promptFeedback && jsonResponse.promptFeedback.blockReason) {
            Logger.log(`Prompt blocked for ${vendorName}. Reason: ${jsonResponse.promptFeedback.blockReason}. Details: ${JSON.stringify(jsonResponse.promptFeedback.safetyRatings)}`);
            return { error: `AI prompt blocked (Pass 1) - ${jsonResponse.promptFeedback.blockReason}`, details: JSON.stringify(jsonResponse.promptFeedback.safetyRatings) };
        }
        return { error: "No candidates from AI (Pass 1)", details: responseBody.substring(0,500) };
      }

      const candidate = jsonResponse.candidates[0];
      if (candidate.finishReason && candidate.finishReason !== "STOP" && candidate.finishReason !== "MAX_TOKENS") {
        Logger.log(`Gemini finished with reason: ${candidate.finishReason} for ${vendorName} (Pass 1). Details: ${JSON.stringify(candidate.safetyRatings || {})}. Full response: ${responseBody}`);
        return { error: `AI processing issue (Pass 1) - ${candidate.finishReason}`, details: (candidate.safetyRatings ? JSON.stringify(candidate.safetyRatings) : responseBody.substring(0,500)) };
      }

      if (!candidate.content || !candidate.content.parts || candidate.content.parts.length === 0 || !candidate.content.parts[0].text) {
        Logger.log(`Unexpected candidate structure for ${vendorName} (Pass 1). Candidate: ${JSON.stringify(candidate)}. Full response: ${responseBody}`);
        return { error: "Malformed AI response structure (Pass 1)", details: `Candidate: ${JSON.stringify(candidate)}`.substring(0,500) };
      }
      // --- END OF MODIFIED ERROR CHECKING ---

      const candidateText = candidate.content.parts[0].text;
      Logger.log(`Raw candidate text for ${vendorName} (Pass 1): ${candidateText}`);
      try {
        return JSON.parse(candidateText);
      } catch (e) {
        Logger.log(`Error parsing JSON from Gemini Pass 1 for ${vendorName}: ${candidateText}. Error: ${e}`);
        return { error: "Failed to parse AI response JSON (Pass 1)", details: candidateText };
      }
    } else {
      Logger.log(`Error calling Gemini API Pass 1 for ${vendorName}: ${responseCode} - ${responseBody}`);
      return { error: `API Error ${responseCode} (Pass 1)`, details: responseBody.substring(0, 500) };
    }
  } catch (e) {
    Logger.log(`Exception during API call Pass 1 for ${vendorName}: ${e.toString()}`);
    // Adding more detail to the exception log
    if (e.stack) {
        Logger.log(`Stack trace for exception: ${e.stack}`);
    }
    return { error: "Exception during API call (Pass 1)", details: e.toString() };
  }
}

function analyzeAllVendors_Pass1() {
  const ui = SpreadsheetApp.getUi();
  const ss = SpreadsheetApp.getActiveSpreadsheet();
  const sheet = ss.getSheetByName(VENDOR_SHEET_NAME);

  if (!sheet) {
    ui.alert(`Sheet "${VENDOR_SHEET_NAME}" not found for Pass 1.`);
    return false; // Indicate failure
  }
  Logger.log("Starting Pass 1: Initial Vendor Categorization");
  SpreadsheetApp.getActiveSpreadsheet().toast("Starting Pass 1: Initial Categorization...", "Vendor Analysis Suite", -1);


  const departments = getDepartmentList();
  if (!departments) return false;
  const departmentListString = departments.join(", ");

  const lastRow = sheet.getLastRow();
  if (lastRow < START_ROW) {
    ui.alert("No data rows found in sheet for Pass 1.");
    return false;
  }
  const dataRange = sheet.getRange(START_ROW, 1, lastRow - START_ROW + 1, SUGGESTION_COL);
  const data = dataRange.getValues();

  let processedInThisRun = 0;
  let skippedCount = 0;

  for (let i = 0; i < data.length; i++) {
    const currentRowInSheet = START_ROW + i;
    const vendorName = data[i][VENDOR_NAME_COL - 1];
    const existingDepartment = data[i][DEPARTMENT_COL - 1];
    const existingDescription = data[i][DESCRIPTION_COL - 1];
    const existingSuggestion = data[i][SUGGESTION_COL - 1];

    if (!vendorName || vendorName.toString().trim() === "") {
      skippedCount++;
      continue;
    }

    if (existingDescription && existingDescription.toString().includes("Error processing: API Error 429")) {
      Logger.log(`Row ${currentRowInSheet} (Pass 1) for "${vendorName}" had 429 error. Clearing desc & reprocessing.`);
      sheet.getRange(currentRowInSheet, DESCRIPTION_COL).setValue("");
    } else if (existingDepartment && existingDepartment.toString().trim() !== "" &&
      existingDescription && existingDescription.toString().trim() !== "" &&
      !existingDescription.toString().startsWith("Error processing:") &&
      existingSuggestion && existingSuggestion.toString().trim() !== "" &&
      existingSuggestion.toString().trim() !== "Consolidate") { // Allow re-processing if it's "Consolidate" from an old run
      Logger.log(`Skipping row ${currentRowInSheet} (Pass 1) for "${vendorName}" as it seems processed and not 'Consolidate'.`);
      skippedCount++;
      continue;
    }

    SpreadsheetApp.getActiveSpreadsheet().toast(`Pass 1: Processing vendor ${processedInThisRun + 1}/${data.length - skippedCount}: ${vendorName}`, "Vendor Analysis Suite", 5);
    Logger.log(`Pass 1 Processing row ${currentRowInSheet}: ${vendorName}`);

    const analysis = getVendorAnalysisFromGemini_Pass1(vendorName, departmentListString);

    if (analysis && !analysis.error) {
      let finalDepartment = analysis.department;
      if (finalDepartment && !departments.includes(finalDepartment)) {
        Logger.log(`Warning (Pass 1): Gemini suggested dept "${finalDepartment}" for "${vendorName}" (not in list). Using suggestion. Review recommended.`);
      }
      sheet.getRange(currentRowInSheet, DEPARTMENT_COL).setValue(finalDepartment || "Needs Review: No Dept");
      sheet.getRange(currentRowInSheet, DESCRIPTION_COL).setValue(analysis.description || "Needs Review: No Desc");
      sheet.getRange(currentRowInSheet, SUGGESTION_COL).setValue(analysis.suggestion_action || "Needs Review: No Action");
      processedInThisRun++;
    } else {
      const errorMsg = `Error (Pass 1): ${analysis ? analysis.error : 'Unknown'}${analysis && analysis.details ? ' - ' + analysis.details : ''}`;
      Logger.log(`Failed Pass 1 for ${vendorName}: ${errorMsg}`);
      sheet.getRange(currentRowInSheet, DESCRIPTION_COL).setValue(errorMsg.substring(0, 450));
      sheet.getRange(currentRowInSheet, DEPARTMENT_COL).setValue("Error");
      sheet.getRange(currentRowInSheet, SUGGESTION_COL).setValue("Error");
    }
    Utilities.sleep(5500); 
  }
  Logger.log(`Pass 1 Finished. Processed: ${processedInThisRun}, Skipped: ${skippedCount}`);
  SpreadsheetApp.getActiveSpreadsheet().toast("Pass 1: Initial Categorization Complete!", "Vendor Analysis Suite", 5);
  return true; // Indicate success
}

function getConsolidationSuggestionForDepartmentFromGemini_Pass2(departmentName, vendorListString) {
  const prompt = `
You are an acquisition analyst for a remote-first, lean software company, aiming to optimize vendor spend.
We are reviewing vendors within the '${departmentName}' department to identify significant cost consolidation opportunities.
When recommending a vendor to keep, prioritize those that offer robust functionality suitable for a primary business tool and represent good value. Generally, a significantly higher-cost vendor is expected to offer more comprehensive features than a very low-cost one performing a similar basic function. Avoid selecting extremely low-cost vendors as the primary if more substantial, albeit more expensive, alternatives exist that cover the same core need more broadly.

Here is a list of vendors assigned to this department, their 1-line descriptions, and 12-month costs:

${vendorListString}

Considering their functions and costs, and the prioritization guidelines above:
1.  Identify any groups of 2 or more vendors within this list that offer significantly overlapping functionalities and represent a clear consolidation opportunity that would lead to cost savings or improved efficiency.
2.  For each such consolidation opportunity:
    a.  List the vendor names (exact match from the list provided) to be consolidated (these are the ones to be eliminated).
    b.  Recommend which single vendor name (exact match from the list provided) from that group should be kept as the primary tool. Consider its likely feature set (inferred from description and cost) and its suitability as a central tool for a lean, remote software company.
    c.  Briefly state the reason for your recommendation, referencing cost-effectiveness, feature suitability, or potential for broad adoption.
3.  If no significant consolidation opportunities are found (e.g., vendors are distinct or consolidation offers trivial savings), state "No clear consolidation opportunities found in this group."

Format your response strictly as a single JSON object with a key "consolidations". The value should be an array of objects. Each object in the array represents a distinct consolidation opportunity and must have these exact keys:
- "vendors_to_consolidate": An array of vendor names (strings, exact match from input) that are redundant and should be considered for termination.
- "recommended_vendor_to_keep": The name (string, exact match from input) of the vendor to keep from the group.
- "reason": A brief explanation (string).

If no consolidations are identified, the JSON response must be: { "consolidations": [] }
Do not include any text before or after this JSON object.
Ensure vendor names in your response exactly match the names provided in the input list.
`;

  const payload = {
    "contents": [{ "parts": [{ "text": prompt }] }],
    "generationConfig": {
      "temperature": 0.2, "topK": 1, "topP": 1,
      "maxOutputTokens": 1536, // Increased for potentially larger department lists & JSON
      "responseMimeType": "application/json"
    }
  };
  const options = {
    'method': 'post', 'contentType': 'application/json',
    'payload': JSON.stringify(payload), 'muteHttpExceptions': true
  };

  try {
    Logger.log(`Sending prompt to Gemini (Pass 2) for department: ${departmentName}`);
    const response = UrlFetchApp.fetch(GEMINI_API_ENDPOINT, options); // Uses global GEMINI_API_ENDPOINT
    const responseCode = response.getResponseCode();
    const responseBody = response.getContentText();

    if (responseCode === 200) {
      const jsonResponse = JSON.parse(responseBody);
      if (jsonResponse.candidates && jsonResponse.candidates[0].content && jsonResponse.candidates[0].content.parts[0].text) {
        const candidateText = jsonResponse.candidates[0].content.parts[0].text;
        Logger.log(`Raw candidate text for ${departmentName} consolidation (Pass 2): ${candidateText}`);
        try {
          return JSON.parse(candidateText);
        } catch (e) {
          Logger.log(`Error parsing JSON from Gemini Pass 2 for ${departmentName}: ${candidateText}. Error: ${e}`);
          return { error: "Failed to parse AI response JSON (Pass 2)", details: candidateText };
        }
      } else {
        Logger.log(`Unexpected Gemini Pass 2 response structure for ${departmentName}: ${responseBody}`);
        return { error: "Unexpected AI response structure (Pass 2)", details: responseBody };
      }
    } else {
      Logger.log(`Error calling Gemini API Pass 2 for ${departmentName}: ${responseCode} - ${responseBody}`);
      return { error: `API Error ${responseCode} (Pass 2)`, details: responseBody.substring(0, 500) };
    }
  } catch (e) {
    Logger.log(`Exception during API call Pass 2 for ${departmentName}: ${e.toString()}`);
    return { error: "Exception during API call (Pass 2)", details: e.toString() };
  }
}

function analyzeConsolidationsByDepartment_Pass2() {
  const ui = SpreadsheetApp.getUi();
  const ss = SpreadsheetApp.getActiveSpreadsheet();
  const sheet = ss.getSheetByName(VENDOR_SHEET_NAME);

  if (!sheet) {
    ui.alert(`Sheet "${VENDOR_SHEET_NAME}" not found for Pass 2.`);
    return false; // Indicate failure
  }
  Logger.log("Starting Pass 2: Consolidation Analysis by Department");
  SpreadsheetApp.getActiveSpreadsheet().toast("Starting Pass 2: Consolidation Analysis...", "Vendor Analysis Suite", -1);


  // Ensure the Consolidation Notes column header exists
  if (sheet.getRange(1, CONSOLIDATION_NOTES_COL).getValue() === "") {
    sheet.getRange(1, CONSOLIDATION_NOTES_COL).setValue("Consolidation Target/Notes");
    SpreadsheetApp.flush();
  }

  const lastRow = sheet.getLastRow();
  if (lastRow < START_ROW) {
    ui.alert("No data found in the sheet for Pass 2.");
    return false;
  }

  // Read all relevant data: Name, Department, Cost, Description, current Suggestion (from Pass 1)
  // Read up to CONSOLIDATION_NOTES_COL to get all existing data, though we mainly use up to SUGGESTION_COL for input
  const dataRange = sheet.getRange(START_ROW, 1, lastRow - START_ROW + 1, CONSOLIDATION_NOTES_COL);
  const allVendorDataValues = dataRange.getValues();
  const allVendorObjects = [];

  for (let i = 0; i < allVendorDataValues.length; i++) {
    const row = allVendorDataValues[i];
    if (row[VENDOR_NAME_COL - 1] && row[DEPARTMENT_COL - 1]) {
      allVendorObjects.push({
        rowIndex: START_ROW + i,
        name: row[VENDOR_NAME_COL - 1],
        department: row[DEPARTMENT_COL - 1],
        cost: row[COST_COL - 1],
        description: row[DESCRIPTION_COL - 1],
        currentSuggestion: row[SUGGESTION_COL - 1] // Suggestion from Pass 1
      });
    }
  }

  if (allVendorObjects.length === 0) {
    ui.alert("No vendors with names and departments found for Pass 2.");
    return false;
  }

  const vendorsByDepartment = {};
  allVendorObjects.forEach(vendor => {
    if (!vendorsByDepartment[vendor.department]) {
      vendorsByDepartment[vendor.department] = [];
    }
    // Only include vendors that don't have "Error" as department and have a description
    if (vendor.department !== "Error" && vendor.description && !vendor.description.toString().startsWith("Error")) {
        vendorsByDepartment[vendor.department].push(vendor);
    }
  });
  
  let departmentsProcessed = 0;
  const totalDepartmentsToProcess = Object.keys(vendorsByDepartment).length;

  for (const departmentName in vendorsByDepartment) {
    const vendorsInDept = vendorsByDepartment[departmentName];
    departmentsProcessed++;
    SpreadsheetApp.getActiveSpreadsheet().toast(`Pass 2: Dept ${departmentsProcessed}/${totalDepartmentsToProcess}: ${departmentName} (${vendorsInDept.length} vendors)`, "Vendor Analysis Suite", 5);

    if (vendorsInDept.length <= 1) {
      Logger.log(`Skipping department "${departmentName}" (Pass 2) - ${vendorsInDept.length} vendor(s).`);
      continue;
    }

    Logger.log(`Analyzing department (Pass 2): "${departmentName}" with ${vendorsInDept.length} vendors.`);
    let vendorListString = "";
    vendorsInDept.forEach(v => {
      vendorListString += `- Vendor Name: ${v.name}, Description: ${v.description || "N/A"}, Cost: $${v.cost || 0}\n`;
    });

    const geminiResponse = getConsolidationSuggestionForDepartmentFromGemini_Pass2(departmentName, vendorListString);

    if (geminiResponse && geminiResponse.consolidations && geminiResponse.consolidations.length > 0) {
      Logger.log(`Consolidation opps for ${departmentName} (Pass 2): ${JSON.stringify(geminiResponse.consolidations)}`);
      geminiResponse.consolidations.forEach(opp => {
        const recommendedVendorName = opp.recommended_vendor_to_keep;
        const vendorsToConsolidateNames = opp.vendors_to_consolidate || [];

        if (vendorsToConsolidateNames.length === 1 && vendorsToConsolidateNames[0] === recommendedVendorName) {
          Logger.log(`Skipping self-consolidation for "${recommendedVendorName}" in dept "${departmentName}" (Pass 2).`);
          const selfConsolidatingVendor = allVendorObjects.find(v => v.name === recommendedVendorName && v.department === departmentName);
          if (selfConsolidatingVendor) {
            const currentSugg = sheet.getRange(selfConsolidatingVendor.rowIndex, SUGGESTION_COL).getValue();
            // If not already "Optimize (Primary)", set to original or "Optimize"
            if (currentSugg !== "Optimize (Primary)" && currentSugg !== "Optimize") {
                 sheet.getRange(selfConsolidatingVendor.rowIndex, SUGGESTION_COL).setValue(selfConsolidatingVendor.currentSuggestion || "Optimize");
                 sheet.getRange(selfConsolidatingVendor.rowIndex, CONSOLIDATION_NOTES_COL).setValue("Reviewed; no direct peer in dept.");
            }
          }
          return; 
        }

        vendorsToConsolidateNames.forEach(vendorNameToConsolidate => {
          if (vendorNameToConsolidate === recommendedVendorName) return; // Don't mark the 'to_keep' as 'consolidate'

          const vendorToUpdate = allVendorObjects.find(v => v.name === vendorNameToConsolidate && v.department === departmentName);
          if (vendorToUpdate) {
            sheet.getRange(vendorToUpdate.rowIndex, SUGGESTION_COL).setValue("Consolidate");
            sheet.getRange(vendorToUpdate.rowIndex, CONSOLIDATION_NOTES_COL).setValue(`Consolidate into ${recommendedVendorName} (Reason: ${opp.reason || 'AI suggestion'})`);
            Logger.log(`Marked "${vendorNameToConsolidate}" to consolidate into "${recommendedVendorName}" (Pass 2).`);
          }
        });

        const recommendedVendorObject = allVendorObjects.find(v => v.name === recommendedVendorName && v.department === departmentName);
        if (recommendedVendorObject) {
          const currentSugg = sheet.getRange(recommendedVendorObject.rowIndex, SUGGESTION_COL).getValue();
          if (currentSugg !== "Consolidate") { // Don't overwrite if it's somehow marked to be consolidated itself
             sheet.getRange(recommendedVendorObject.rowIndex, SUGGESTION_COL).setValue("Optimize (Primary)");
             sheet.getRange(recommendedVendorObject.rowIndex, CONSOLIDATION_NOTES_COL).setValue(`Primary tool. ${opp.reason || ""}`.trim());
             Logger.log(`Marked "${recommendedVendorName}" as primary/optimize (Pass 2).`);
          } else {
            Logger.log(`INFO (Pass 2): "${recommendedVendorName}" recommended to keep, but is already "Consolidate". Review manually.`);
          }
        } else {
            Logger.log(`Warning (Pass 2): Recommended vendor_to_keep "${recommendedVendorName}" not found in dept "${departmentName}". AI Output: ${JSON.stringify(opp)}`);
        }
      });
    } else if (geminiResponse && geminiResponse.consolidations) {
        Logger.log(`No clear consolidation opps by AI for dept "${departmentName}" (Pass 2).`);
    } else {
        Logger.log(`Error or no valid response from Gemini for dept "${departmentName}" (Pass 2). Resp: ${JSON.stringify(geminiResponse)}`);
    }
    Utilities.sleep(2500); // Rate limit API calls
  }
  Logger.log("Pass 2: Consolidation Analysis Finished.");
  SpreadsheetApp.getActiveSpreadsheet().toast("Pass 2: Consolidation Analysis Complete!", "Vendor Analysis Suite", 5);
  return true; // Indicate success
}

// --- Orchestrator Function for the Full Analysis Suite ---
function runFullVendorAnalysisSuite() {
  const ui = SpreadsheetApp.getUi();
  const startTime = new Date();
  Logger.log("Starting Full Vendor Analysis Suite...");
  ui.alert("Vendor Analysis Suite", "This will run in two passes:\n1. Initial vendor categorization.\n2. Consolidation analysis by department.\nThis may take significant time. Do not close the sheet. Monitor logs for progress.", ui.ButtonSet.OK);

  // Pass 1: Initial Categorization
  const pass1Success = analyzeAllVendors_Pass1();
  if (!pass1Success) {
    ui.alert("Full Analysis Suite Error", "Pass 1 (Initial Categorization) failed. Check logs. Aborting further processing.", ui.ButtonSet.OK);
    Logger.log("Full Vendor Analysis Suite aborted due to Pass 1 failure.");
    return;
  }
  SpreadsheetApp.flush(); // Ensure all Pass 1 writes are committed

  // Pass 2: Consolidation Analysis
  const pass2Success = analyzeConsolidationsByDepartment_Pass2();
  if (!pass2Success) {
    ui.alert("Full Analysis Suite Warning", "Pass 2 (Consolidation Analysis) encountered an issue or did not complete successfully. Please check logs. Pass 1 data is saved.", ui.ButtonSet.OK);
    Logger.log("Full Vendor Analysis Suite: Pass 2 did not complete successfully.");
    // Continue to show completion message as Pass 1 might be useful
  }
  SpreadsheetApp.flush(); // Ensure all Pass 2 writes are committed

  const endTime = new Date();
  const durationMinutes = Math.round((endTime - startTime) / 60000);
  Logger.log(`Full Vendor Analysis Suite completed in approximately ${durationMinutes} minutes.`);
  ui.alert("Vendor Analysis Suite Complete", `Both passes have finished. Process duration: approx. ${durationMinutes} minutes. Please review the sheet, especially columns E (Suggestions) and F (Consolidation Target/Notes).`, ui.ButtonSet.OK);
}

// --- Utility function to clear specific error messages (optional, but can be helpful) ---
function clearSpecificErrors() {
  const ui = SpreadsheetApp.getUi();
  const ss = SpreadsheetApp.getActiveSpreadsheet();
  const sheet = ss.getSheetByName(VENDOR_SHEET_NAME);

  if (!sheet) {
    ui.alert(`Sheet "${VENDOR_SHEET_NAME}" not found.`);
    return;
  }

  const response = ui.prompt(
      'Clear Error Cells',
      'Enter the error text to find and clear in the Description column (e.g., "Error processing: API Error 429"). This will clear the Department, Description, and Suggestion for matching rows.',
      ui.ButtonSet.OK_CANCEL);

  if (response.getSelectedButton() !== ui.Button.OK) return;
  const errorTextToClear = response.getResponseText();
  if (!errorTextToClear || errorTextToClear.trim() === "") {
    ui.alert("No error text provided.");
    return;
  }

  const lastRow = sheet.getLastRow();
  // Fetching Department, Description, Suggestion columns
  const range = sheet.getRange(START_ROW, DEPARTMENT_COL, lastRow - START_ROW + 1, 3); 
  const values = range.getValues();
  let clearedCount = 0;

  for (let i = 0; i < values.length; i++) {
    // Index 1 in `values[i]` corresponds to Description (since range starts at DEPARTMENT_COL)
    if (values[i][DESCRIPTION_COL - DEPARTMENT_COL] && values[i][DESCRIPTION_COL - DEPARTMENT_COL].toString().includes(errorTextToClear)) {
      values[i][DEPARTMENT_COL - DEPARTMENT_COL] = ""; // Clear Department (index 0 relative to range)
      values[i][DESCRIPTION_COL - DEPARTMENT_COL] = ""; // Clear Description (index 1 relative to range)
      values[i][SUGGESTION_COL - DEPARTMENT_COL] = "";  // Clear Suggestion (index 2 relative to range)
      clearedCount++;
    }
  }

  if (clearedCount > 0) {
    range.setValues(values);
    ui.alert("Clear Complete", `${clearedCount} rows with "${errorTextToClear}" in Description have had their Department, Description, and Suggestion cleared.`, ui.ButtonSet.OK);
  } else {
    ui.alert("No Matches", `No rows found with "${errorTextToClear}" in the Description column.`, ui.ButtonSet.OK);
  }
}

// ===================================================================================
// --- NEW SECTION: Strategic Opportunity Analysis & Reporting ---
// ===================================================================================

/**
 * Main function to perform strategic opportunity analysis and reporting.
 */
function runStrategicOpportunityAnalysis() {
  const ui = SpreadsheetApp.getUi();
  ui.alert("Strategic Opportunity Analysis", "This will calculate departmental cost impacts and identify top strategic savings opportunities. This may take a moment.", ui.ButtonSet.OK);
  Logger.log("Starting Strategic Opportunity Analysis...");
  SpreadsheetApp.getActiveSpreadsheet().toast("Running Strategic Analysis...", "Analysis", -1);

  const ss = SpreadsheetApp.getActiveSpreadsheet();
  const vendorSheet = ss.getSheetByName(VENDOR_SHEET_NAME); // Uses global VENDOR_SHEET_NAME

  if (!vendorSheet) {
    ui.alert(`Source sheet "${VENDOR_SHEET_NAME}" not found.`);
    Logger.log(`Strategic Analysis Aborted: Source sheet "${VENDOR_SHEET_NAME}" not found.`);
    return;
  }

  const departmentalImpact = calculateDepartmentalImpact(vendorSheet);
  if (!departmentalImpact) {
    Logger.log("Strategic Analysis Aborted: Failed to calculate departmental impact.");
    return; 
  }
  populateDepartmentalImpactSheet(ss, departmentalImpact);
  SpreadsheetApp.flush(); 

  populateTop3StrategicOpportunities(ss, departmentalImpact.summary);

  SpreadsheetApp.getActiveSpreadsheet().toast("Strategic Analysis Complete!", "Analysis", 5);
  Logger.log("Strategic Opportunity Analysis finished.");
  ui.alert("Strategic Analysis Complete", `The "${SA_DEPT_IMPACT_SHEET_NAME}" and "${SA_TOP_3_OPPS_SHEET_NAME}" sheets have been updated.`, ui.ButtonSet.OK);
}

/**
 * Calculates before/after costs by department based on vendor suggestions.
 */
function calculateDepartmentalImpact(vendorSheet) {
  const lastRow = vendorSheet.getLastRow();
  if (lastRow < START_ROW) { 
    SpreadsheetApp.getUi().alert("No data rows found in the vendor sheet.");
    return null;
  }

  const vendorDataValues = vendorSheet.getRange(START_ROW, 1, lastRow - START_ROW + 1, CONSOLIDATION_NOTES_COL).getValues();

  const impact = {
    byDepartment: {}, 
    summary: { 
      totalBefore: 0, totalAfter: 0, totalSavingsTerminate: 0,
      totalSavingsConsolidate: 0, totalSavingsOptimize: 0, overallTotalSavings: 0,
      facilitiesTerminationSavings: 0 
    }
  };

  vendorDataValues.forEach(row => {
    const dept = row[DEPARTMENT_COL - 1]; // Global DEPARTMENT_COL
    const cost = parseFloat(row[COST_COL - 1]) || 0; // Global COST_COL
    const suggestion = row[SUGGESTION_COL - 1]; // Global SUGGESTION_COL

    if (!dept || cost === 0) return; 

    if (!impact.byDepartment[dept]) {
      impact.byDepartment[dept] = { before: 0, after: 0, savingsTerminate: 0, savingsConsolidate: 0, savingsOptimize: 0, totalSavings: 0 };
    }

    impact.byDepartment[dept].before += cost;
    impact.byDepartment[dept].after += cost; 
    impact.summary.totalBefore += cost;
    impact.summary.totalAfter += cost;

    let savedAmount = 0;

    if (suggestion === "Terminate") {
      savedAmount = cost;
      impact.byDepartment[dept].savingsTerminate += savedAmount;
      impact.summary.totalSavingsTerminate += savedAmount;
      if (dept.toLowerCase().includes("facilities")) { // More robust check for "facilities"
          impact.summary.facilitiesTerminationSavings += savedAmount;
      }
    } else if (suggestion === "Consolidate") {
      savedAmount = cost;
      impact.byDepartment[dept].savingsConsolidate += savedAmount;
      impact.summary.totalSavingsConsolidate += savedAmount;
    } else if (suggestion === "Optimize" || suggestion === "Optimize (Primary)") {
      savedAmount = cost * SA_OPTIMIZATION_SAVINGS_PERCENTAGE; // Global SA_OPTIMIZATION_SAVINGS_PERCENTAGE
      impact.byDepartment[dept].savingsOptimize += savedAmount;
      impact.summary.totalSavingsOptimize += savedAmount;
    }

    if (savedAmount > 0) {
      impact.byDepartment[dept].after -= savedAmount;
      impact.byDepartment[dept].totalSavings += savedAmount;
      impact.summary.totalAfter -= savedAmount;
      impact.summary.overallTotalSavings += savedAmount;
    }
  });
  Logger.log("Departmental impact calculated.");
  return impact;
}

/**
 * Populates the 'Departmental Cost Impact' sheet.
 */
function populateDepartmentalImpactSheet(ss, departmentalImpact) {
  let sheet = ss.getSheetByName(SA_DEPT_IMPACT_SHEET_NAME); // Global SA_DEPT_IMPACT_SHEET_NAME
  if (!sheet) {
    sheet = ss.insertSheet(SA_DEPT_IMPACT_SHEET_NAME);
    Logger.log(`Sheet "${SA_DEPT_IMPACT_SHEET_NAME}" created.`);
  }
  sheet.clearContents(); 

  const headers = [
    "Department", "Current Annual Spend", "Savings (Termination)",
    "Savings (Consolidation)", "Savings (Optimization)", "Total Estimated Savings",
    "Estimated Future Annual Spend"
  ];
  sheet.getRange(1, 1, 1, headers.length).setValues([headers]).setFontWeight("bold");

  let rowNum = 2;
  const data = [];
  for (const deptName in departmentalImpact.byDepartment) {
    const d = departmentalImpact.byDepartment[deptName];
    data.push([ deptName, d.before, d.savingsTerminate, d.savingsConsolidate, d.savingsOptimize, d.totalSavings, d.after ]);
  }

  if (data.length > 0) {
    sheet.getRange(rowNum, 1, data.length, headers.length).setValues(data);
    sheet.getRange(rowNum, 2, data.length, headers.length -1).setNumberFormat("$#,##0.00");
  }
  
  rowNum += data.length;
  const summary = departmentalImpact.summary;
  sheet.getRange(rowNum, 1, 1, headers.length).setValues([[
    "TOTALS", summary.totalBefore, summary.totalSavingsTerminate, summary.totalSavingsConsolidate,
    summary.totalSavingsOptimize, summary.overallTotalSavings, summary.totalAfter
  ]]).setFontWeight("bold");
  sheet.getRange(rowNum, 2, 1, headers.length -1).setNumberFormat("$#,##0.00");

  sheet.autoResizeColumns(1, headers.length);
  Logger.log(`Sheet "${SA_DEPT_IMPACT_SHEET_NAME}" populated.`);
}

/**
 * Populates the 'Top 3 Opportunities' sheet with strategic themes.
 */
function populateTop3StrategicOpportunities(ss, summaryData) { 
  let sheet = ss.getSheetByName(SA_TOP_3_OPPS_SHEET_NAME); // Global SA_TOP_3_OPPS_SHEET_NAME
  if (!sheet) {
    sheet = ss.insertSheet(SA_TOP_3_OPPS_SHEET_NAME);
    Logger.log(`Sheet "${SA_TOP_3_OPPS_SHEET_NAME}" created.`);
  }
  sheet.clearContents();

  const headers = ["Opportunity Title", "Explanation", "Estimated Annual Savings (USD)"];
  sheet.getRange(1, 1, 1, headers.length).setValues([headers]).setFontWeight("bold");

  const opportunities = [];

  const opp1_savings = summaryData.facilitiesTerminationSavings;
  if (opp1_savings > 0) {
    opportunities.push({
      title: "Eliminate Physical Office Infrastructure Costs",
      explanation: "Transitioning to a fully remote operational model allows for the termination of all physical office leases and associated facilities expenses, yielding substantial direct cost reductions.",
      savings: opp1_savings,
      raw_details_for_ai: `Focus on terminating all contracts related to physical office spaces (department typically named 'Facilities' or similar) due to the remote-first model. Total calculated savings from these terminations amount to $${opp1_savings.toFixed(2)}.`
    });
  } else { opportunities.push({ title: "Eliminate Physical Office Infrastructure Costs", explanation: "No significant savings identified from terminating facilities costs, or no facilities vendors marked for termination. This may indicate facilities are already minimal or require further review.", savings: 0, raw_details_for_ai: `No facilities vendors were marked for termination or their costs were zero.`}); }

  const opp2_savings = summaryData.totalSavingsConsolidate;
   if (opp2_savings > 0) {
    opportunities.push({
      title: "Streamline Software Stack via Consolidation",
      explanation: "Consolidate redundant software tools and services across departments by migrating functionalities to preferred primary solutions. This reduces licensing fees, simplifies IT management, and can improve operational efficiency.",
      savings: opp2_savings,
      raw_details_for_ai: `Aggregate savings from all vendors marked 'Consolidate'. The total direct saving by eliminating these redundant tools is $${opp2_savings.toFixed(2)}. Assumed primary tools can absorb load without immediate cost increase.`
    });
  } else { opportunities.push({ title: "Streamline Software Stack via Consolidation", explanation: "No significant savings identified from software consolidation, or no vendors were marked 'Consolidate'. Further review of tool overlap may be beneficial.", savings: 0, raw_details_for_ai: `No vendors were marked for 'Consolidate' or their costs were zero.` }); }

  const opp3_savings = summaryData.totalSavingsOptimize;
   if (opp3_savings > 0) {
    opportunities.push({
      title: "Optimize Spend on Essential Software & Services",
      explanation: `Negotiate improved terms, right-size user licenses, and explore alternative subscription plans for essential software and services. A conservative ${SA_OPTIMIZATION_SAVINGS_PERCENTAGE*100}% average saving is estimated across these tools.`,
      savings: opp3_savings,
      raw_details_for_ai: `Apply an estimated ${SA_OPTIMIZATION_SAVINGS_PERCENTAGE*100}% saving to all vendors marked 'Optimize' or 'Optimize (Primary)'. Total estimated saving is $${opp3_savings.toFixed(2)}.`
    });
  } else { opportunities.push({ title: "Optimize Spend on Essential Software & Services", explanation: "No significant optimization savings identified with current assumptions, or no vendors were marked for 'Optimize'. Detailed review of key vendor contracts could still yield savings.", savings: 0, raw_details_for_ai: `No vendors were marked for 'Optimize' or their costs were zero, or the ${SA_OPTIMIZATION_SAVINGS_PERCENTAGE*100}% estimate resulted in zero savings.` }); }
  
  let reportRowNum = 2;
  for (let i = 0; i < opportunities.length; i++) {
    const opp = opportunities[i];
    SpreadsheetApp.getActiveSpreadsheet().toast(`Refining Strategic Opportunity ${i+1}/${opportunities.length} with AI...`, "Reporting", 5);
    
    const aiInputOpp = {
        type: opp.title.includes("Facilities") ? "Facilities Elimination" : (opp.title.includes("Consolidation") ? "Software Consolidation" : "Spend Optimization"),
        title_raw: opp.title, 
        details: opp.raw_details_for_ai, 
        savings: opp.savings
    };
    
    const refinedData = getStrategicRefinedTextFromGemini(aiInputOpp); // Call to helper below

    if (refinedData && !refinedData.error) {
      sheet.getRange(reportRowNum, 1).setValue(refinedData.title); 
      sheet.getRange(reportRowNum, 2).setValue(refinedData.explanation); 
    } else {
      sheet.getRange(reportRowNum, 1).setValue(opp.title);
      sheet.getRange(reportRowNum, 2).setValue(opp.explanation + (refinedData ? ` (AI Refinement Error: ${refinedData.error} - ${refinedData.details || ''})` : " (AI Refinement Error)"));
      Logger.log(`Error refining strategic opportunity with AI for: ${opp.title}. Error: ${refinedData ? refinedData.error : 'Unknown'}`);
    }
    sheet.getRange(reportRowNum, 3).setValue(opp.savings).setNumberFormat("$#,##0.00");
    reportRowNum++;
    
    if (i < opportunities.length -1) Utilities.sleep(1500); 
  }
  
  sheet.autoResizeColumns(1, headers.length);
  Logger.log(`Sheet "${SA_TOP_3_OPPS_SHEET_NAME}" populated with strategic opportunities.`);
}

/**
 * Helper function to call Gemini to refine strategic opportunity title and explanation. (NEWLY ADDED/CONFIRMED)
 * @param {object} opportunity - An object with keys: type, title_raw, details, savings.
 * @return {object} - An object with keys: title, explanation, or an error object.
 */
function getStrategicRefinedTextFromGemini(opportunity) {
  const prompt = `
You are an analyst preparing a summary report for management of a remote-first, lean software company.
Given the following strategic cost-saving opportunity:
Opportunity Theme: "${opportunity.type}"
Current Title Suggestion: "${opportunity.title_raw}"
Supporting Details/Calculation: "${opportunity.details}"
Total Estimated Annual Savings: $${opportunity.savings.toFixed(2)}

Please generate the following:
1.  "Opportunity Title": A concise, impactful, and professional title for this strategic opportunity (max 6-7 words).
2.  "Brief Explanation": A 1-2 sentence explanation of the opportunity, its strategic benefit to a lean, remote-first company, and how the savings are achieved.

Format your response strictly as a single JSON object like this:
{
  "title": "REFINED_OPPORTUNITY_TITLE",
  "explanation": "REFINED_BRIEF_EXPLANATION_TEXT"
}
Do not include any text before or after the JSON object.
`;

  const payload = {
    "contents": [{ "parts": [{ "text": prompt }] }],
    "generationConfig": { "temperature": 0.3, "topK": 1, "topP": 1, "maxOutputTokens": 300, "responseMimeType": "application/json" }
  };
  const options = { 'method': 'post', 'contentType': 'application/json', 'payload': JSON.stringify(payload), 'muteHttpExceptions': true };

  try { // Using the same robust error checking structure
    const response = UrlFetchApp.fetch(GEMINI_API_ENDPOINT, options);
    const responseCode = response.getResponseCode();
    const responseBody = response.getContentText();
    if (responseCode === 200) {
      const jsonResponse = JSON.parse(responseBody);
      if (!jsonResponse.candidates || jsonResponse.candidates.length === 0) {
        Logger.log(`No candidates for strategic opp refinement: ${opportunity.title_raw}. Resp: ${responseBody}`);
        if (jsonResponse.promptFeedback && jsonResponse.promptFeedback.blockReason) return { error: `AI prompt blocked - ${jsonResponse.promptFeedback.blockReason}`, details: JSON.stringify(jsonResponse.promptFeedback.safetyRatings) };
        return { error: "No candidates from AI", details: responseBody.substring(0, 200) };
      }
      const candidate = jsonResponse.candidates[0];
      if (candidate.finishReason && candidate.finishReason !== "STOP" && candidate.finishReason !== "MAX_TOKENS") {
        Logger.log(`Gemini finished with reason: ${candidate.finishReason} for strategic opp refinement. Details: ${JSON.stringify(candidate.safetyRatings || {})}`);
        return { error: `AI processing issue - ${candidate.finishReason}`, details: (candidate.safetyRatings ? JSON.stringify(candidate.safetyRatings) : responseBody.substring(0, 200)) };
      }
      if (!candidate.content || !candidate.content.parts || candidate.content.parts.length === 0 || !candidate.content.parts[0].text) {
        Logger.log(`Unexpected candidate structure for strategic opp refinement. Cand: ${JSON.stringify(candidate)}`);
        return { error: "Malformed AI response structure", details: `Candidate: ${JSON.stringify(candidate)}`.substring(0, 200) };
      }
      const candidateText = candidate.content.parts[0].text;
      Logger.log(`Raw refined text for strategic opp ${opportunity.title_raw}: ${candidateText}`);
      try { return JSON.parse(candidateText); }
      catch (e) { Logger.log(`Error parsing JSON for refined strategic opp text: ${candidateText}. Error: ${e}`); return { error: "Failed to parse AI response JSON", details: candidateText }; }
    } else { Logger.log(`Error calling Gemini for strategic opp refinement: ${responseCode} - ${responseBody}`); return { error: `API Error ${responseCode}`, details: responseBody.substring(0, 200) }; }
  } catch (e) { Logger.log(`Exception during API call for strategic opp refinement: ${e.toString()}`); if (e.stack) { Logger.log(`Stack trace for exception (Strategic Refinement): ${e.stack}`); } return { error: "Exception during API call", details: e.toString() }; }
}

function onOpen() {
  SpreadsheetApp.getUi()
    .createMenu('Vendor Analysis Suite')
    .addItem('Run Full Analysis (Pass 1 & 2)', 'runFullVendorAnalysisSuite')
    .addSeparator()
    .addItem('Run Pass 1 Only (Initial Categorization)', 'analyzeAllVendors_Pass1')
    .addItem('Run Pass 2 Only (Consolidation Check)', 'analyzeConsolidationsByDepartment_Pass2')
    .addSeparator()
    .addItem('Generate Strategic Opportunities & Impact Report', 'runStrategicOpportunityAnalysis') 
    .addSeparator()
    .addItem('Utility: Clear Specific Error Rows', 'clearSpecificErrors')
    .addToUi();
}
