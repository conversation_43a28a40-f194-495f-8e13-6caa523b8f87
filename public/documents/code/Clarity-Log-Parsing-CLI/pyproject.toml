[build-system]
requires = ["setuptools>=42", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "log-parser"
version = "0.1.0"
description = "speedrunning the assignment"
readme = "README.md"
requires-python = ">=3.6"
authors = [
    {name = "peekknuf", email = "<EMAIL>"}
]
classifiers = [
    "Programming Language :: Python :: 3",
    "License :: OSI Approved :: MIT License",
    "Operating System :: OS Independent",
]
dependencies = [
    "pytest>=7.0.0",
    "textual>=0.47.1"
]

[project.scripts]
log-parser = "src.__main__:main"

[tool.setuptools]
packages = [
    "src",
    "src.parser",
    "src.processing",
    "src.utils",
    "src.tui",
    "src.tui.screens",
    "src.cli"
]

[tool.pytest.ini_options]
testpaths = ["tests"]
python_files = "test_*.py"
python_functions = "test_*"
