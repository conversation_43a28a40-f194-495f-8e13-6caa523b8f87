{"ai_email_processing_project": "AI Email and Orders Processing System built with Python, OpenAI API, Langchain, RAG, and Google Sheets API. Available at https://github.com/peekknuf/Generative-AI-emails. Engineered intelligent system for automated email classification and order processing with RAG-enhanced contextual understanding. Designed scalable architecture for email processing workflows, implemented RAG techniques for enhanced context retention, and built robust API integrations with error handling and monitoring. Eliminated manual email processing overhead, demonstrating ability to automate complex business workflows using Python API integration, RAG implementation, email processing automation, and Google Sheets integration.", "infosec_streaming_pipeline": "Real-Time InfoSec Streaming Pipeline built with Python, Kafka, PostgreSQL, Grafana, and Docker. Available at https://github.com/peekknuf/streaming_pipeline. Architected end-to-end streaming pipeline for security log ingestion, processing, and real-time alerting. Designed Kafka streaming architecture with moderate load of 30k events per second, implemented real-time data processing with monitoring, and built comprehensive observability with Grafana dashboards. Demonstrates expertise in stream processing, real-time systems, and production monitoring through Kafka streaming, Python data processing, PostgreSQL integration, real-time monitoring, and Docker containerization.", "gengo_data_generator": "Gengo: High-Performance Relational Data Generator built with Golang, CLI, concurrent processing, supporting Parquet, CSV, and JSON Lines formats. Available at https://github.com/peekknuf/Gengo. Fast CLI-based data generation tool creating large-scale synthetic relational datasets in normalized 3NF e-commerce schema. Optimized for performance using Go's concurrency model for millions of rows, implements 3NF relational model with foreign key constraints, supports weighted sampling for realistic fact table generation, multiple output formats with Snappy compression, and interactive CLI interface with automatic file naming per table. Achieved 10x performance improvement over Python prototype, demonstrating Golang concurrency, CLI development, database schema design, performance optimization, and data generation algorithms.", "log_parser_tui_cli": "Log Parser with TUI/CLI built with Python, CLI, TUI (Textual), and real-time monitoring capabilities. Available at https://github.com/peekknuf/Clarity-Log-Parsing-CLI. Python tool for analyzing connection logs with dual-interface architecture supporting both interactive TUI and scriptable CLI modes. Features dual-interface architecture (TUI + CLI), real-time stream processing with directory monitoring, batch processing with flexible time range filtering, responsive terminal interface with keyboard navigation, and modular codebase for maintainability. Demonstrates Python CLI development, TUI with Textual library, real-time file monitoring, log parsing algorithms, and modular architecture design.", "vendor_analysis_optimization": "Vendor Spend Analysis & Optimization built with Google Apps Script, Google Sheets, Gemini API, and AI/ML Integration. Available at https://github.com/peekknuf/VendorAnalysis. AI-powered vendor analysis workflow for approximately 400 vendors with strategic recommendations using automation. Features automated workflow with Google Apps Script, Gemini API integration with error handling and rate limiting, multi-phase processing pipeline, structured JSON output parsing, and custom spreadsheet functions and menus. Automated complex vendor analysis providing quantified cost-saving recommendations, demonstrating Google Apps Script development, AI API integration, data processing pipelines, error handling, and business automation.", "projects_technical_summary": "Portfolio includes Python projects, Golang CLI tools, Kafka streaming systems, AI integration, RAG implementation, data generation tools, log parsing utilities, automation scripts, API integration, real-time processing systems, performance optimization, Docker containers, database design, and TUI interfaces. These projects demonstrate full-stack data engineering capabilities, from high-performance data generation to real-time streaming and AI-powered automation.", "personal_portfolio_ai_assistant": "Personal Portfolio & AI Assistant built with React, TypeScript, Supabase, PostgreSQL, pgvector, Google Gemini, and RAG. Available at https://personal-opal-nine.vercel.app/. Architected and built a personal portfolio website featuring a sophisticated AI assistant. The assistant uses a custom Retrieval-Augmented Generation (RAG) pipeline with pgvector for efficient semantic search over a corpus of personal and professional documents. The system includes a multi-layer caching strategy and intent detection to provide fast and accurate responses, demonstrating advanced skills in full-stack development, AI/ML integration, and database optimization."}