# User Journey

The user journey for Omaku<PERSON> can be outlined as follows:

1. **Introduction to Omakub**: The user discovers Omakub as a Linux distribution that offers a curated, easy-to-use experience.
2. **Initial Setup**: The user installs Omakub and is presented with a simple, guided setup process that asks for basic information, such as language and timezone.
3. **Application Selection**: The user is given the option to choose between different applications, such as Neovim vs. VSCode, to customize their experience.
4. **Theme Selection**: The user can choose from a variety of themes to personalize their system's appearance.
5. **Exploration**: The user starts exploring the system, discovering the pre-installed applications and features.
6. **Ongoing Use**: The user continues to use Omakub, potentially discovering new features and applications, and developing their own workflows and habits.

# Experiment Ideas

Here are three possible experiment ideas to test the impact of different customization options and application selections on customer satisfaction:

NOTE: WE DO NOT USE ANY SORT OF TRACKING OF USERS, EVERYTHING IS BASED OFF THE INFORMATION GIVEN BY THE USER, WE DO NOT COLLECT THEIR DATA, IT WOULD GO AGAINST LINUX PHILOSOPHY.
## Experiment 1: Application Choice

- **Test Cells**:
  - Cell A: Neovim as the default text editor
  - Cell B: VSCode as the default text editor
  - Cell C: User choice between Neovim and VSCode during setup
- **Hypothesis**: Allowing users to choose between Neovim and VSCode will increase customer satisfaction, as users will be able to select the editor that best fits their needs.
- **Metrics**: Track user satisfaction surveys, application usage patterns, and feedback forms to measure the impact of this experiment.

## Experiment 2: Theme Selection

- **Test Cells**:
  - Cell A: Limited theme selection (e.g., 3-5 options)
  - Cell B: Expanded theme selection (e.g., 10-15 options)
  - Cell C: User-submitted themes (allowing users to create and share their own themes)
- **Hypothesis**: Offering a wider range of themes will increase customer satisfaction, as users will be able to personalize their system's appearance to their liking.
- **Metrics**: Track user engagement, theme selection patterns, and feedback forms to measure the impact of this experiment.

## Experiment 3: Streamlined Installation Options

- **Test Cells**:
  - Cell A: Full installation with all pre-installed applications
  - Cell B: Streamlined installation with a reduced set of pre-installed applications (based on usage patterns and user feedback)
  - Cell C: Modular installation, allowing users to select which applications to install during setup
- **Hypothesis**: Streamlining the installation process and offering modular installation options will increase customer satisfaction, as users will be able to tailor their system to their specific needs.
- **Metrics**: Track user satisfaction surveys, installation time, and feedback forms to measure the impact of this experiment.
