services:
  postgres:
    image: postgres:14
    restart: on-failure
    container_name: ${DOCKER_CONTAINER}
    env_file:
      - .env
    environment:
      - POSTGRES_DB=${POSTGRES_SCHEMA}
      - POSTGRES_USER=${POSTGRES_USER}
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD}
    ports:
      - "${HOST_PORT}:5432"
    volumes:
      - ./:/bootcamp/
      - ./data.dump:/docker-entrypoint-initdb.d/data.dump
      - ./scripts/init-db.sh:/docker-entrypoint-initdb.d/init-db.sh
      - postgres-data:/var/lib/postgresql/data
  pgadmin:
    image: dpage/pgadmin4
    restart: on-failure
    container_name: pgadmin
    environment:
      - PGADMIN_DEFAULT_EMAIL=${PGADMIN_EMAIL}
      - PGADMIN_DEFAULT_PASSWORD=${PGADMIN_PASSWORD}
    ports:
      - "${PGADMIN_PORT}:80"
    volumes:
      - pgadmin-data:/var/lib/pgadmin
volumes:
  postgres-data:
  pgadmin-data:
