# task_3_inquiry_rag.py
import pandas as pd
import time
from langchain.schema import Document
from langchain.vectorstores import FAISS
from config import ( GPT_MODEL, EMBEDDING_STRATEGY, EMBEDDING_MODEL_NAME,
                     OPENAI_API_KEY, RAG_NUM_RETRIEVED_DOCS )

def setup_embeddings():
    """Initializes the chosen embedding model."""
    print(f"  Initializing embeddings using strategy: '{EMBEDDING_STRATEGY}'")
    if EMBEDDING_STRATEGY == 'openai':
        try:
            from langchain_openai import OpenAIEmbeddings
            if not OPENAI_API_KEY or "FALLBACK" in OPENAI_API_KEY:
                raise ValueError("OpenAI API Key needed for OpenAI embeddings is missing or invalid.")
            embeddings = OpenAIEmbeddings(
                model=EMBEDDING_MODEL_NAME,
                openai_api_key=OPENAI_API_KEY
            )
            print(f"  Initialized OpenAI Embeddings model: {EMBEDDING_MODEL_NAME}")
            return embeddings
        except ImportError:
             print("ERROR: `langchain-openai` not installed. Cannot use OpenAI embeddings.")
             raise
        except Exception as e:
            print(f"ERROR: Failed to initialize OpenAI Embeddings: {e}")
            raise
    elif EMBEDDING_STRATEGY == 'huggingface':
        try:
            from langchain_community.embeddings import HuggingFaceEmbeddings
            model_kwargs = {'device': 'cpu'}
            encode_kwargs = {'normalize_embeddings': False}
            embeddings = HuggingFaceEmbeddings(
                model_name=EMBEDDING_MODEL_NAME,
                model_kwargs=model_kwargs,
                encode_kwargs=encode_kwargs
            )
            print(f"  Initialized HuggingFace Embeddings model: {EMBEDDING_MODEL_NAME}")
            return embeddings
        except ImportError:
            print("ERROR: `langchain_community` or `sentence-transformers` not installed.")
            raise
        except Exception as e:
            print(f"ERROR: Failed to initialize HuggingFace Embeddings: {e}")
            raise
    else:
        raise ValueError(f"Unsupported EMBEDDING_STRATEGY: {EMBEDDING_STRATEGY}")

def build_vector_store(products_df, embeddings):
    """Builds the FAISS vector store from product data."""
    print("  Preparing product documents...")
    product_documents = []
    # Document preparation logic (same as notebook)
    required_cols = ['product_id', 'name', 'category', 'description', 'seasons', 'price']
    if not all(col in products_df.columns for col in required_cols): raise ValueError(f"products_df missing required RAG columns.")
    for _, row in products_df.iterrows():
        price_str = f"${row['price']:.2f}" if pd.notna(row['price']) else "N/A"
        content = f"Name: {row.get('name', 'N/A')}\nID: {row.get('product_id', 'N/A')}\nCat: {row.get('category', 'N/A')}\nDesc: {row.get('description', 'N/A')}\nSeason: {row.get('seasons', 'N/A')}\nPrice: {price_str}"
        metadata = {"product_id": str(row.get('product_id', 'N/A')), "name": str(row.get('name', 'N/A'))}
        product_documents.append(Document(page_content=content, metadata=metadata))
    print(f"  Created {len(product_documents)} documents.")

    print("  Creating FAISS vector store...")
    start_time = time.time()
    try:
        vector_store = FAISS.from_documents(product_documents, embeddings)
        end_time = time.time()
        print(f"  FAISS vector store created in {end_time - start_time:.2f} seconds.")
        return vector_store
    except Exception as e:
        print(f"ERROR creating FAISS vector store: {e}")
        raise # Propagate error

def handle_inquiries(inquiry_emails, vector_store, client):
    """Handles inquiry emails using RAG."""
    inquiry_responses = []
    print("\n--- Task 3: Starting Inquiry Handling ---")
    print(f"  Processing {len(inquiry_emails)} inquiry emails...")

    if inquiry_emails.empty:
        print("  No inquiry emails to process.")
        return pd.DataFrame(columns=['email ID', 'response'])

    for row in inquiry_emails.itertuples(index=False):
        # Extract email details... (same as notebook)
        email_id_internal = row.email_id
        subject = str(row.subject) if pd.notna(row.subject) else ""
        question = str(row.message) if pd.notna(row.message) else ""

        if not question.strip():
             print(f"  Skipping Email ID: {email_id_internal} (Empty message)")
             inquiry_responses.append({"email ID": email_id_internal, "response": "[Auto-Reply: Empty message.]"})
             continue

        print(f"\n  -- Processing Inquiry ID: {email_id_internal} --")
        print(f"    Question: {question[:100]}...")
        generated_response = "Error: Failed to generate response."

        try:
            # --- RAG Retrieval ---
            print("      Retrieving documents...")
            retrieved_docs = vector_store.similarity_search(question, k=RAG_NUM_RETRIEVED_DOCS)
            print(f"      Retrieved {len(retrieved_docs)} docs.")

            # --- RAG Augmentation ---
            if not retrieved_docs: context_string = "No relevant product information found."
            else: context_string = "\n\n---\n\n".join([doc.page_content for doc in retrieved_docs])

            # --- RAG Generation ---
            system_prompt_rag = ( # The RAG system prompt
                 "You are 'ChicBot', a helpful AI assistant for the 'Chic Threads' fashion store. "
                "Your task is to answer customer questions based *ONLY* on the provided product information context retrieved from our catalog. "
                "Do NOT use any external knowledge or information not present in the context. "
                "If the provided context does not contain the answer to the question, clearly state that you don't have that specific information based on the provided details (e.g., 'Based on the product details I have, I cannot find information about [specific topic]'). "
                "Maintain a friendly, professional, and helpful tone appropriate for a fashion brand. "
                "Address the customer politely (e.g., 'Hi there,', 'Thanks for asking!')."
                "Structure your answer clearly."
            )
            user_prompt_rag = (
                f"Please answer the following customer question based *only* on the provided product context.\n\n"
                f"## Customer Question:\n{question}\n\n"
                f"## Product Context:\n{context_string}\n\n"
                f"## Your Answer:"
            )
            print("      Generating response via GPT...")
            response = client.chat.completions.create(
                model=GPT_MODEL,
                messages=[ {"role": "system", "content": system_prompt_rag}, {"role": "user", "content": user_prompt_rag}],
                temperature=0.3, max_tokens=400
            )
            generated_response = response.choices[0].message.content.strip()
            print(f"      Generated Response (Snippet): {generated_response[:100]}...")

        except Exception as e:
            print(f"    Error processing inquiry {email_id_internal}: {e}")

        inquiry_responses.append({"email ID": email_id_internal, "response": generated_response})
        # --- End Inquiry Processing ---

    inquiry_response_df_output = pd.DataFrame(inquiry_responses)
    if not inquiry_response_df_output.empty:
        inquiry_response_df_output = inquiry_response_df_output[['email ID', 'response']]
    else:
        inquiry_response_df_output = pd.DataFrame(columns=['email ID', 'response'])

    print("--- Task 3: Inquiry Handling Finished ---")
    return inquiry_response_df_output
