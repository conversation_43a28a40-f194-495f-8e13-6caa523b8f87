# utils.py
import pandas as pd
import gspread
from google.auth import default
from gspread_dataframe import set_with_dataframe
import string
import re
from config import GOOGLE_CREDENTIALS_FILE


# --- Google Sheets Authentication ---
def get_gspread_client():
    """Authenticates with Google Sheets using service account credentials."""
    try:
        scopes = [
            "https://www.googleapis.com/auth/spreadsheets",
            "https://www.googleapis.com/auth/drive",
        ]  # Add drive scope if needed
        creds = Credentials.from_service_account_file(
            GOOGLE_CREDENTIALS_FILE, scopes=scopes
        )
        client = gspread.authorize(creds)
        print("Google Sheets Client authenticated successfully.")
        return client
    except FileNotFoundError:
        print(f"ERROR: Credentials file '{GOOGLE_CREDENTIALS_FILE}' not found.")
        raise
    except Exception as e:
        print(f"ERROR: Failed to authenticate Google Sheets Client: {e}")
        raise


# --- Data Reading ---
def read_data_frame(gc, document_id, sheet_name):
    """Reads a sheet from a Google Sheet into a pandas DataFrame."""
    try:
        print(
            f"Attempting to read sheet '{sheet_name}' from document ID '{document_id}'..."
        )
        spreadsheet = gc.open_by_key(document_id)  # Use open_by_key for ID
        worksheet = spreadsheet.worksheet(sheet_name)
        data = worksheet.get_all_values()  # Get header and data
        headers = data.pop(0)
        df = pd.DataFrame(data, columns=headers)
        # Ensure email_id is string if column exists
        if "email_id" in df.columns:
            df["email_id"] = df["email_id"].astype(str)
        print(
            f"Successfully loaded data from sheet: '{sheet_name}' (Shape: {df.shape})"
        )
        return df
    except gspread.exceptions.SpreadsheetNotFound:
        print(
            f"ERROR: Spreadsheet not found (ID: {document_id}). Check ID and permissions."
        )
        raise
    except gspread.exceptions.WorksheetNotFound:
        print(f"ERROR: Worksheet '{sheet_name}' not found in spreadsheet.")
        raise
    except Exception as e:
        print(f"Error loading data from Google Sheet '{sheet_name}': {e}")
        raise


# --- Data Writing ---
def write_dataframe_to_sheet(
    gc, output_sheet_name, target_worksheet_name, df, required_header=None
):
    """Writes a DataFrame to a specific worksheet in a Google Sheet."""
    print(
        f"\n--- Writing to Sheet: '{output_sheet_name}', Worksheet: '{target_worksheet_name}' ---"
    )
    if df is None or df.empty:
        print("  Skipping write: DataFrame is None or empty.")
        return

    try:
        output_document = gc.open(output_sheet_name)
        print(f"  Opened spreadsheet '{output_sheet_name}'.")
        try:
            worksheet = output_document.worksheet(target_worksheet_name)
            print(f"  Accessed worksheet '{target_worksheet_name}'.")

            # Optional: Header Check/Update
            if required_header:
                try:
                    print("  Checking header...")
                    num_cols = len(required_header[0])
                    header_range = (
                        gspread.utils.rowcol_to_a1(1, 1)
                        + ":"
                        + gspread.utils.rowcol_to_a1(1, num_cols)
                    )
                    existing_header = worksheet.get(
                        header_range, value_render_option="UNFORMATTED_VALUE"
                    )
                    if existing_header != required_header:
                        print(f"  Updating header in '{target_worksheet_name}'...")
                        worksheet.update(header_range, required_header)
                        worksheet.format(header_range, {"textFormat": {"bold": True}})
                        print("  Header updated.")
                    else:
                        print("  Header OK.")
                except Exception as header_err:
                    print(f"  Warning: Header check/update failed. Err: {header_err}.")

            # Write data
            print(f"  Writing {len(df)} records (starting row 2)...")
            set_with_dataframe(
                worksheet=worksheet,
                dataframe=df,
                row=2,  # Assume header is row 1
                include_index=False,
                include_column_header=False,
                resize=False,
            )
            print(f"  Successfully wrote data.")
            print(f"Link: https://docs.google.com/spreadsheets/d/{output_document.id}")

        except gspread.exceptions.WorksheetNotFound:
            print(f"  ERROR: Worksheet '{target_worksheet_name}' not found!")
        except Exception as e:
            print(f"  ERROR writing to worksheet '{target_worksheet_name}': {e}")

    except gspread.exceptions.SpreadsheetNotFound:
        print(f"ERROR: Output spreadsheet '{output_sheet_name}' not found.")
    except Exception as e:
        print(f"ERROR interacting with Google Sheet: {e}")


# --- Product Lookup Helper ---
# Import thefuzz here if not already done globally
try:
    from thefuzz import process as fuzz_process

    thefuzz_available = True
except ImportError:
    print("Warning: 'thefuzz' library not found for utils.py.")
    thefuzz_available = False


def find_product_id_enhanced(name_or_id_input, stock_df_with_cleaned_name, threshold):
    """Enhanced lookup using pre-cleaned names."""
    # Ensure thefuzz is available
    if not thefuzz_available:
        print("ERROR: thefuzz library not available for product lookup.")
        return None  # Or handle differently

    if not name_or_id_input or not isinstance(name_or_id_input, str):
        return None
    input_str = str(name_or_id_input).strip()
    cleaned_input = input_str.lower().translate(
        str.maketrans("", "", string.punctuation.replace("-", ""))
    )
    cleaned_input = " ".join(cleaned_input.split())

    # Direct ID Match
    potential_direct_ids = {input_str, input_str.upper(), input_str.lower()}
    for pid in potential_direct_ids:
        if pid in stock_df_with_cleaned_name.index:
            return pid

    # Exact Cleaned Name Match
    # Assumes 'cleaned_name' column is already present on the passed DataFrame
    if "cleaned_name" not in stock_df_with_cleaned_name.columns:
        print(
            "ERROR in find_product_id_enhanced: 'cleaned_name' column missing from input stock_df."
        )
        return None
    matches_name = stock_df_with_cleaned_name[
        stock_df_with_cleaned_name["cleaned_name"] == cleaned_input
    ]
    if not matches_name.empty:
        return matches_name.index[0]

    # Regex ID Pattern (simplified example)
    try:
        potential_ids_regex = re.findall(r"\b([a-z]{2,4}\d{3,})\b", cleaned_input)
        if potential_ids_regex:
            for pid_text in potential_ids_regex:
                normalized_pid = pid_text.upper()
                if normalized_pid in stock_df_with_cleaned_name.index:
                    return normalized_pid
    except Exception:
        pass  # Ignore regex errors silently for now

    # Fuzzy Name Matching
    try:
        name_list = stock_df_with_cleaned_name["cleaned_name"].tolist()
        best_match_tuple = fuzz_process.extractOne(
            cleaned_input, name_list, score_cutoff=threshold
        )
        if best_match_tuple:
            matched_name, _ = best_match_tuple
            fuzzy_matches = stock_df_with_cleaned_name[
                stock_df_with_cleaned_name["cleaned_name"] == matched_name
            ]
            if not fuzzy_matches.empty:
                return fuzzy_matches.index[0]
    except Exception as fuzz_err:
        print(f"DEBUG: Fuzzy matching error: {fuzz_err}")

    return None
