# task_2_orders.py
import pandas as pd
import json
import string
from utils import find_product_id_enhanced
from config import GPT_MOD<PERSON>, FUZZY_MATCH_THRESHOLD

def process_orders(order_request_emails, products_df, client):
    """Processes order request emails, updates stock, and generates responses."""
    order_status_records = []
    processed_order_details = {} # For response generation context

    print("\n--- Task 2: Starting Order Processing ---")

    # --- Prepare Product Data for Stock Tracking ---
    print("  Preparing product data for stock tracking...")
    if 'stock' not in products_df.columns or 'product_id' not in products_df.columns or 'name' not in products_df.columns:
         raise ValueError("Products DataFrame missing required columns: product_id, name, stock")
    products_df['stock'] = pd.to_numeric(products_df['stock'], errors='coerce').fillna(0).astype(int)
    current_stock_df = products_df.copy().set_index('product_id')
    # Add cleaned name column for lookup
    current_stock_df['cleaned_name'] = current_stock_df['name'].str.lower().str.translate(
        str.maketrans('', '', string.punctuation.replace('-', ''))
    ).str.split().str.join(" ")
    print(f"  Working stock DataFrame created (Index: product_id, Columns added: cleaned_name).")

    # --- Process Each Order Request ---
    print(f"  Processing {len(order_request_emails)} order requests...")
    if order_request_emails.empty:
        print("  No order requests to process.")
        # Return empty results if no orders
        return pd.DataFrame(columns=['email ID', 'product ID', 'quantity', 'status']), \
               pd.DataFrame(columns=['email ID', 'response']), \
               current_stock_df # Return the (unmodified) stock df

    for row in order_request_emails.itertuples(index=False):
        # Extract email details... (same as notebook)
        email_id_internal = row.email_id
        subject = str(row.subject) if pd.notna(row.subject) else ""
        message = str(row.message) if pd.notna(row.message) else ""
        email_items_summary = [] # Summary for this specific email

        if not message.strip():
            print(f"  Skipping Email ID: {email_id_internal} (Empty message)")
            processed_order_details[email_id_internal] = [{"status": "skipped_empty_message"}]
            continue

        print(f"\n  -- Processing Email ID: {email_id_internal} --")

        # --- 2a: Extract Items using AI (with Flexible Parsing) ---
        print("    Extracting items via AI...")
        extraction_prompt_system = ( # The robust extraction prompt
            "You are an AI parsing customer emails for a fashion store. "
            "Carefully read the email message and extract specific products and quantities the customer explicitly wants to order. "
            "Ignore questions or general text. Focus only on clear purchase intent.\n"
            "CRITICAL: Respond ONLY with a valid JSON object containing a single key named 'items'. The value of 'items' must be a list of JSON objects. Example: `{\"items\": [{\"product_name_or_id\": \"SKU123\", \"quantity\": 1}, {\"product_name_or_id\": \"Another Name\", \"quantity\": 2}]}`.\n"
            "Each object in the 'items' list must have exactly two keys: "
            "'product_name_or_id' (string, the exact name or ID mentioned by the customer) and "
            "'quantity' (integer or the string 'all remaining'/'the rest'/'all you have'). \n"
            "If NO items are explicitly requested for order in the message, respond with `{\"items\": []}`.\n"
            "Do NOT add any explanations or introductory text outside the JSON structure."
        )
        extraction_prompt_user = f"Parse this email message for order items:\n\n{message}"
        extracted_items_raw = []
        try:
            response = client.chat.completions.create(
                model=GPT_MODEL,
                messages=[{"role": "system", "content": extraction_prompt_system},
                          {"role": "user", "content": extraction_prompt_user}],
                temperature=0.0,
                response_format={"type": "json_object"}
            )
            json_string = response.choices[0].message.content
            print(f"      AI Raw Response: {json_string}")
            # --- Flexible Parsing Logic ---
            try:
                parsed_response = json.loads(json_string)
                found_list = False
                if isinstance(parsed_response, dict):
                    for key, value in parsed_response.items():
                        if isinstance(value, list):
                            print(f"      Found list under key: '{key}'.")
                            extracted_items_raw = value
                            found_list = True
                            break
                    if not found_list:
                         if not parsed_response: print("      AI returned empty {}.")
                         else: print(f"      Warning: No list found in dict. Keys: {list(parsed_response.keys())}")
                else: print(f"      Warning: AI response not a dict.")
            except json.JSONDecodeError as json_err:
                print(f"      Error: Invalid JSON. {json_err}")
                email_items_summary.append({"status": "extraction_error", "detail": "Invalid JSON from AI"})
            # --- End Flexible Parsing ---

            # --- Validation ---
            valid_items = []
            all_remaining_phrases = {"all remaining", "the rest", "all you have"}
            print(f"      Validating {len(extracted_items_raw)} potential items...");
            for item in extracted_items_raw:
                # Validation logic... (same as notebook)
                if not isinstance(item, dict): continue
                p_name = item.get('product_name_or_id')
                p_qty = item.get('quantity')
                is_all_remaining_request = False; validation_reasons = []
                if not isinstance(p_name, str) or not p_name.strip(): validation_reasons.append("bad name"); p_name = ""
                else: p_name = p_name.strip()
                if isinstance(p_qty, str) and p_qty.strip().lower() in all_remaining_phrases: is_all_remaining_request = True; p_qty = p_qty.strip().lower()
                elif not (isinstance(p_qty, int) and p_qty > 0): validation_reasons.append("bad qty")
                if not validation_reasons: valid_items.append({"product_name_or_id": p_name,"original_quantity": p_qty,"is_all_remaining": is_all_remaining_request})
                else: print(f"      Warning: Skipping invalid item: {item}. Reasons: {validation_reasons}")
            extracted_items = valid_items
            print(f"      Validated {len(extracted_items)} items.")
            # --- End Validation ---

        except Exception as e:
            print(f"    Error during AI extraction: {e}")
            if not any(e_item.get("status") == "extraction_error" for e_item in email_items_summary):
                email_items_summary.append({"status": "extraction_error", "detail": f"API Error: {e}"})
            extracted_items = []

        # --- 2b: Process Each Validated Item ---
        if not extracted_items and not any(e.get("status") == "extraction_error" for e in email_items_summary):
             print("    No valid items found for processing.")
             if not email_items_summary: email_items_summary.append({"status": "no_items_found"})

        for item_idx, item in enumerate(extracted_items):
            # Item processing logic... (same as notebook)
            # Calls find_product_id_enhanced(..., current_stock_df, FUZZY_MATCH_THRESHOLD)
            # Updates current_stock_df.loc[product_id_found, 'stock'] directly
            # Appends results to order_status_records
            # Appends detailed summary to email_items_summary
            requested_name_id = item['product_name_or_id']; original_requested_quantity = item['original_quantity']; is_all_remaining = item['is_all_remaining']
            print(f"      Processing item {item_idx+1}: '{requested_name_id}'")
            status = "error_processing"; product_id_found = None; product_name = "N/A"; available_stock = 0; effective_quantity = 0; product_id_for_sheet = f"Error ({requested_name_id})"
            try:
                product_id_found = find_product_id_enhanced(requested_name_id, current_stock_df, FUZZY_MATCH_THRESHOLD)
                if product_id_found:
                    product_id_for_sheet = product_id_found
                    try:
                        product_info = current_stock_df.loc[product_id_found]; product_name = product_info.get('name', 'N/A'); available_stock = int(product_info.get('stock', 0))
                        if is_all_remaining: effective_quantity = available_stock if available_stock > 0 else 0
                        elif isinstance(original_requested_quantity, int): effective_quantity = original_requested_quantity
                        else: effective_quantity = 0 # Should not happen post-validation
                        if effective_quantity <= 0: status = "out of stock"
                        elif available_stock >= effective_quantity:
                             status = "created"; current_stock_df.loc[product_id_found, 'stock'] -= effective_quantity
                        else: status = "out of stock"
                    except KeyError: status = "error_stock_lookup"; product_id_for_sheet = f"Stock Error ({product_id_found})"
                    except Exception as stock_err: print(f"Err accessing stock: {stock_err}"); status = "error_stock_update"; product_id_for_sheet = f"Stock Error ({product_id_found})"
                else: status = "product_not_found"; product_id_for_sheet = f"Not Found ({requested_name_id})"; effective_quantity = 1
            except Exception as find_err: print(f"Err during lookup: {find_err}"); status = "error_lookup"; product_id_for_sheet = f"Lookup Error ({requested_name_id})"; effective_quantity = 1
            # Record results
            print(f"        -> Outcome: Status='{status}', ID='{product_id_for_sheet}', Qty={effective_quantity}")
            order_status_records.append({"email ID": email_id_internal, "product ID": product_id_for_sheet, "quantity": effective_quantity, "status": status})
            email_items_summary.append({"requested_name": requested_name_id, "original_request": original_requested_quantity, "product_id": product_id_found, "product_name": product_name, "processed_quantity": effective_quantity, "status": status, "stock_before_order": available_stock, "is_all_remaining_request": is_all_remaining})
        # --- End Item Processing Loop ---

        if not extracted_items and not email_items_summary:
            if not any(e.get("status") == "extraction_error" for e in email_items_summary):
                 email_items_summary.append({"status": "no_valid_items_found"})

        processed_order_details[email_id_internal] = email_items_summary
        # --- End Email Processing ---

    # --- Filter Status Records ---
    order_status_df_full = pd.DataFrame(order_status_records)
    if not order_status_df_full.empty:
        output_statuses = ['created', 'out of stock']
        order_status_df_output = order_status_df_full[order_status_df_full['status'].isin(output_statuses)].copy()
        order_status_df_output = order_status_df_output[['email ID', 'product ID', 'quantity', 'status']]
    else:
        order_status_df_output = pd.DataFrame(columns=['email ID', 'product ID', 'quantity', 'status'])

    # --- Generate Responses ---
    order_responses = []
    print("\n  Generating Order Responses...")
    # Get original emails subjects for context
    email_subjects = order_request_emails.set_index('email_id')['subject'].to_dict()

    for email_id_internal, summary_list in processed_order_details.items():
        print(f"    Generating response for: {email_id_internal}")
        subject = email_subjects.get(email_id_internal, "Order Request")
        # Response generation logic... (same as notebook, builds summary_for_ai)
        # Calls client.chat.completions.create with GPT_MODEL
        processing_summary_lines = []; overall_status = "Error - Check Summary"; has_created = False; has_critical_error = False; has_fulfillment_issue = False; initial_error_status = None
        if not summary_list: processing_summary_lines.append("Status: Processing Error"); overall_status = "Error - Processing Failed"; has_critical_error = True; initial_error_status = "processing_failed"
        elif summary_list[0].get("status") in ["skipped_empty_message", "extraction_error", "no_valid_items_found", "no_items_found"]: status_code = summary_list[0]["status"]; detail = summary_list[0].get('detail', 'Could not process.'); processing_summary_lines.append(f"Status: {status_code.replace('_', ' ').title()}"); processing_summary_lines.append(f"Detail: {detail}"); overall_status = f"Error - {status_code.replace('_', ' ').title()}"; has_critical_error = True; initial_error_status = status_code
        else:
            for item_idx, item in enumerate(summary_list):
                status_item = item.get('status', 'error_unknown'); req_name = item.get('requested_name', 'Unknown'); proc_qty = item.get('processed_quantity', 0); line = f"- Item {item_idx+1}: '{req_name}' (Req: '{item.get('original_request', 'N/A')}') -> Status: {status_item.upper()}"
                if status_item == 'created': line += f" (Fulfilled: {proc_qty}, Prod: {item.get('product_name', 'N/A')} [{item.get('product_id', 'N/A')}])"; has_created = True
                elif status_item == 'out of stock': line += f" (Avail: {item.get('stock_before_order', 0)}, Prod: {item.get('product_name', 'N/A')} [{item.get('product_id', 'N/A')}])"; has_fulfillment_issue = True
                elif status_item == 'product_not_found': line += " (Couldn't identify)"; has_fulfillment_issue = True
                elif status_item.startswith('error_'): line += " (Processing issue)"; has_critical_error = True;
                if initial_error_status is None and has_critical_error: initial_error_status = status_item
                else: line += f" ({status_item})"; has_fulfillment_issue = True # Other non-errors are fulfillment issues
                processing_summary_lines.append(line)
            # Determine overall status
            if has_critical_error: overall_status = "Order Partially Fulfilled (with processing issues)" if has_created else f"Error - {initial_error_status.replace('_', ' ').title()}" if initial_error_status else "Error - Processing Order Items Failed"
            elif has_created and not has_fulfillment_issue: overall_status = "Order Fully Confirmed"
            elif has_created and has_fulfillment_issue: overall_status = "Order Partially Fulfilled"
            elif not has_created and has_fulfillment_issue: overall_status = "Order Could Not Be Fulfilled"
            else: overall_status = "Review Required - Check Details" # Fallback
        processing_summary_for_ai = f"Overall Status: {overall_status}\n\nDetails:\n" + "\n".join(processing_summary_lines)
        print(f"      Summary for AI:\n{processing_summary_for_ai[:200]}...")
        # Generate Response using AI
        response_prompt_system = ( # The detailed response prompt
            "You are an AI assistant for 'Chic Threads', a fashion store. Write a professional and helpful email response based on the provided order processing summary.\n"
            "Tone: Friendly, professional, empathetic, fashion-brand aligned.\n\n"
            "Instructions based on 'Overall Status':\n"
            "- If 'Order Fully Confirmed': Enthusiastic thank you. List confirmed items (name, quantity). Mention shipping details follow. Positive tone.\n"
            "- If 'Order Partially Fulfilled': Thank you. Clearly state confirmed items and unfulfilled items (OOS, not found). Explain reasons briefly. Apologize for inconvenience. Suggest alternatives/checking back. Mention confirmed items shipping.\n"
            "- If 'Order Partially Fulfilled (with processing issues)': Similar to partial, but acknowledge a processing difficulty occurred for some items. Apologize. Mention confirmed items shipping. Suggest contacting support if questions about failed items.\n"
            "- If 'Order Could Not Be Fulfilled': Regret you couldn't fulfill. List items and reasons (OOS, not found). Apologize sincerely. Suggest alternatives/checking back/contacting support.\n"
            "- If 'Error - Extraction Error' OR 'Error - No Items Found' OR 'Error - No Valid Items Found' OR 'Error - Processing Order Items Failed' OR 'Review Required - Check Details': Politely state you had trouble processing the specifics (couldn't identify items clearly, or encountered an issue). Ask for clearer details (product names/IDs from website, quantities) or suggest using the online store. Apologize for confusion/trouble.\n"
            "- If 'Error - Processing Failed' OR 'Error - Skipped Empty Message': Apologize for a technical issue or being unable to process (due to empty message). Ask to contact support or try ordering via the website.\n\n"
            "General Guidelines:\n"
            "- Greeting: 'Hi there,' or 'Dear Customer,'.\n"
            "- Refer to their request: 'Regarding your recent order request...'\n"
            "- Structure clearly. Use product names/quantities from summary where appropriate.\n"
            "- Closing: 'Best regards,', 'Sincerely,', followed by 'The Chic Threads Team'.\n"
            "- Do NOT invent details like tracking numbers or specific restock dates unless provided."
        )
        response_prompt_user = (f"Original Subject (context only): {subject}\n\nOrder Processing Summary:\n{processing_summary_for_ai}\n\nCompose the customer response email body:")
        generated_response = "Error: Response generation failed."
        try:
            response = client.chat.completions.create(
                model=GPT_MODEL,
                messages=[{"role": "system", "content": response_prompt_system},{"role": "user", "content": response_prompt_user}],
                temperature=0.5, max_tokens=500
            )
            generated_response = response.choices[0].message.content.strip()
        except Exception as e: print(f"    Error during OpenAI response generation: {e}")
        order_responses.append({"email ID": email_id_internal,"response": generated_response})
    # --- End Response Generation Loop ---

    order_response_df_output = pd.DataFrame(order_responses)
    if not order_response_df_output.empty:
         order_response_df_output = order_response_df_output[['email ID', 'response']]
    else:
         order_response_df_output = pd.DataFrame(columns=['email ID', 'response'])


    print("--- Task 2: Order Processing Finished ---")
    # Return the results and the final state of the stock DataFrame
    # Clean up temp column before returning
    if 'cleaned_name' in current_stock_df.columns:
        current_stock_df = current_stock_df.drop(columns=['cleaned_name'])
    return order_status_df_output, order_response_df_output, current_stock_df
