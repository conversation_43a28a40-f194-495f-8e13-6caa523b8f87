# config.py
import os
from dotenv import load_dotenv

load_dotenv()

# --- OpenAI Configuration ---
OPENAI_API_KEY = os.getenv("OPENAI_API_KEY", "YOUR_API_KEY_FALLBACK")
# Models
GPT_MODEL = "gpt-4o"
# Choose Embedding Strategy: 'openai' or 'huggingface'
EMBEDDING_STRATEGY = "huggingface"  # Default to HF due to key issues
# Specific models based on strategy
if EMBEDDING_STRATEGY == "openai":
    # Ensure key has permissions for this model if selected
    EMBEDDING_MODEL_NAME = "text-embedding-ada-002"  # Or text-embedding-3-small
else:
    EMBEDDING_MODEL_NAME = "all-MiniLM-L6-v2"  # Default HF model

# --- Google Sheets Configuration ---
INPUT_SHEET_DOCUMENT_ID = "14fKHsblfqZfWj3iAaM2oA51TlYfQlFT4WKo52fVaQ9U"
INPUT_EMAILS_SHEET_NAME = "emails"
INPUT_PRODUCTS_SHEET_NAME = "Products"

OUTPUT_SHEET_NAME = "Maksym_I_Solving_Business_Problems_with_AI_Output"
OUTPUT_CLASSIFICATION_SHEET = "email-classification"
OUTPUT_ORDER_STATUS_SHEET = "order-status"
OUTPUT_ORDER_RESPONSE_SHEET = "order-response"
OUTPUT_INQUIRY_RESPONSE_SHEET = "inquiry-response"

GOOGLE_CREDENTIALS_FILE = "credentials.json"

# --- RAG Configuration ---
RAG_NUM_RETRIEVED_DOCS = 4  # Number of documents to retrieve
FUZZY_MATCH_THRESHOLD = 85  # Score cutoff (0-100)

# --- Validation ---
if not OPENAI_API_KEY or "FALLBACK" in OPENAI_API_KEY:
    print(
        "WARNING: OpenAI API Key not found or using fallback. Please set OPENAI_API_KEY environment variable."
    )
