# main.py
import pandas as pd
from IPython.display import display
import time
from openai import OpenAI
import config
import utils
from task_1_classification import run_classification
from task_2_orders import process_orders
from task_3_inquiry_rag import setup_embeddings, build_vector_store, handle_inquiries

def main():
    """Main function to run the email processing pipeline."""
    print("--- Starting Email Processing Pipeline ---")
    start_pipeline_time = time.time()

    # --- Initialization ---
    print("\nInitializing clients...")
    try:
        # Initialize OpenAI client
        if not config.OPENAI_API_KEY or "FALLBACK" in config.OPENAI_API_KEY:
            raise ValueError("OpenAI API Key missing or invalid.")
        openai_client = OpenAI(api_key=config.OPENAI_API_KEY)
        print("OpenAI client initialized.")

        # Initialize Google Sheets client
        gspread_client = utils.get_gspread_client()
    except Exception as e:
        print(f"FATAL: Failed to initialize clients: {e}")
        return # Stop execution if clients fail

    # --- Load Data ---
    print("\nLoading initial data...")
    try:
        emails_df = utils.read_data_frame(gspread_client, config.INPUT_SHEET_DOCUMENT_ID, config.INPUT_EMAILS_SHEET_NAME)
        products_df = utils.read_data_frame(gspread_client, config.INPUT_SHEET_DOCUMENT_ID, config.INPUT_PRODUCTS_SHEET_NAME)
        # Perform necessary type conversions or checks on loaded data here if needed
        if 'price' in products_df.columns:
             products_df['price'] = pd.to_numeric(products_df['price'], errors='coerce')
    except Exception as e:
        print(f"FATAL: Failed to load initial data: {e}")
        return

    # --- Task 1: Classification ---
    try:
        email_classification_df = run_classification(emails_df, openai_client)
        # Merge classifications for filtering later
        emails_df_merged = pd.merge(emails_df, email_classification_df[['email ID', 'category']],
                                   left_on='email_id', right_on='email ID', how='left')
        if 'email ID' in emails_df_merged.columns: emails_df_merged.drop(columns=['email ID'], inplace=True)
        emails_df_merged['category'] = emails_df_merged['category'].fillna('unknown') # Handle missing classifications
    except Exception as e:
        print(f"FATAL: Task 1 (Classification) failed: {e}")
        return

    # --- Task 2: Order Processing ---
    try:
        order_request_emails = emails_df_merged[emails_df_merged['category'] == 'order request'].copy()
        # Sort order requests for sequential processing
        if not order_request_emails.empty:
             order_request_emails['sort_key'] = pd.to_numeric(order_request_emails['email_id'].str.replace('E', '', regex=False), errors='coerce')
             order_request_emails = order_request_emails.dropna(subset=['sort_key']).astype({'sort_key': int}).sort_values(by='sort_key').drop(columns=['sort_key']).reset_index(drop=True)

        order_status_df, order_response_df, final_stock_df = process_orders(order_request_emails, products_df, openai_client)
        print("\nTask 2 Outputs:")
        print("Order Status DataFrame sample:"); display(order_status_df.head())
        print("Order Response DataFrame sample:"); display(order_response_df.head())
        # print("Final Stock DataFrame sample:"); display(final_stock_df.head()) # Optional: display final stock
    except Exception as e:
        print(f"ERROR: Task 2 (Order Processing) encountered an error: {e}")
        # Decide if fatal or continue with empty DFs for Task 2
        order_status_df = pd.DataFrame(columns=['email ID', 'product ID', 'quantity', 'status'])
        order_response_df = pd.DataFrame(columns=['email ID', 'response'])


    # --- Task 3: Inquiry Handling ---
    vector_store = None # Initialize vector_store
    try:
        inquiry_emails = emails_df_merged[emails_df_merged['category'] == 'product inquiry'].copy()
        if not inquiry_emails.empty:
            embeddings = setup_embeddings() # Initialize based on config
            vector_store = build_vector_store(products_df, embeddings)
            inquiry_response_df = handle_inquiries(inquiry_emails, vector_store, openai_client)
            print("\nTask 3 Outputs:")
            print("Inquiry Response DataFrame sample:"); display(inquiry_response_df.head())
        else:
             print("\nSkipping Task 3: No inquiry emails found.")
             inquiry_response_df = pd.DataFrame(columns=['email ID', 'response']) # Create empty df

    except Exception as e:
        print(f"ERROR: Task 3 (Inquiry Handling) encountered an error: {e}")
        # Decide if fatal or continue with empty DF for Task 3
        inquiry_response_df = pd.DataFrame(columns=['email ID', 'response'])


    # --- Write All Outputs to Google Sheet ---
    print("\n--- Writing All Results to Output Google Sheet ---")
    # Define headers for checking
    header_classify = [['email ID', 'category']]
    header_ord_stat = [['email ID', 'product ID', 'quantity', 'status']]
    header_ord_resp = [['email ID', 'response']]
    header_inq_resp = [['email ID', 'response']]

    utils.write_dataframe_to_sheet(gspread_client, config.OUTPUT_SHEET_NAME, config.OUTPUT_CLASSIFICATION_SHEET, email_classification_df, header_classify)
    utils.write_dataframe_to_sheet(gspread_client, config.OUTPUT_SHEET_NAME, config.OUTPUT_ORDER_STATUS_SHEET, order_status_df, header_ord_stat)
    utils.write_dataframe_to_sheet(gspread_client, config.OUTPUT_SHEET_NAME, config.OUTPUT_ORDER_RESPONSE_SHEET, order_response_df, header_ord_resp)
    utils.write_dataframe_to_sheet(gspread_client, config.OUTPUT_SHEET_NAME, config.OUTPUT_INQUIRY_RESPONSE_SHEET, inquiry_response_df, header_inq_resp)

    end_pipeline_time = time.time()
    print("\n--- Email Processing Pipeline Finished ---")
    print(f"Total execution time: {end_pipeline_time - start_pipeline_time:.2f} seconds")

if __name__ == "__main__":
    main()
