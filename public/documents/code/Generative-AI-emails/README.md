# AI Email Processing for Fashion Store Orders & Inquiries

## Objective

This project implements a proof-of-concept application to intelligently process email order requests and customer inquiries for a fashion store. It utilizes Large Language Models (LLMs) and related AI techniques to classify emails, process orders based on product catalog and stock availability, and generate appropriate, context-aware responses.

## Features

*   **Email Classification:** Categorizes incoming emails as either "product inquiry" or "order request" using an LLM (GPT-4o).
*   **Order Processing:**
    *   Extracts requested products and quantities from "order request" emails using an LLM.
    *   Uses fuzzy matching (`thefuzz`) to map potentially misspelled/varied product names to the official catalog.
    *   Verifies product availability against current stock levels.
    *   Updates stock levels dynamically after fulfilling orders.
    *   Records order line item status ("created" or "out of stock").
*   **Order Response Generation:** Generates professional email responses confirming processed orders, explaining out-of-stock items, and maintaining a customer-friendly tone using an LLM (GPT-4o).
*   **Product Inquiry Handling (RAG):**
    *   Uses Retrieval-Augmented Generation (RAG) to answer customer questions in "product inquiry" emails.
    *   Leverages embeddings `all-MiniLM-L6-v2` and a FAISS vector store built from the product catalog.
    *   Retrieves relevant product information based on the inquiry.
    *   Generates answers grounded in the retrieved catalog data using an LLM (GPT-4o), ensuring scalability and avoiding excessive token usage.
*   **Structured Output:** Generates a single Excel spreadsheet containing the results organized into separate sheets.

## Technology Stack

*   **Language:** Python 3.x
*   **Core Libraries:**
    *   `pandas`: Data manipulation and handling spreadsheets.
    *   `openai`: Interacting with the OpenAI API (GPT-4o, Embeddings [if applicable]).
    *   `langchain`, `langchain-openai`, `langchain_community`: Framework for building AI applications, including RAG components, embeddings, vector stores.
    *   `faiss-cpu`: Efficient similarity search library for the vector store.
    *   `thefuzz[speedup]`: Fuzzy string matching for product names.
    *   `gspread`, `gspread-dataframe`, `google-auth`, `google-api-python-client`: Interacting with Google Sheets.
    *   `sentence-transformers`: For local, open-source text embeddings.
    *   `python-dotenv`: Optional, for loading environment variables from a `.env` file.
*   **Environment:** Python script environment (previously Jupyter Notebook / Google Colab).
*   **AI Models:**
    *   ``OpenAI GPT-4o`` (for classification, extraction, generation)
    *   ``all-MiniLM-L6-v2`` (for embeddings in RAG)
