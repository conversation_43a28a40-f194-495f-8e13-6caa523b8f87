# task_1_classify.py
import pandas as pd
from openai import OpenAI
from config import GPT_MODEL

def run_classification(emails_df, client):
    """Classifies emails using OpenAI API."""
    classification_results = []
    print("\n--- Task 1: Starting Email Classification ---")

    if 'email_id' not in emails_df.columns or 'subject' not in emails_df.columns or 'message' not in emails_df.columns:
         raise ValueError("Input emails_df missing required columns: 'email_id', 'subject', 'message'.")

    for row in emails_df.itertuples(index=False):
        email_id = str(row.email_id)
        subject = str(row.subject) if pd.notna(row.subject) else ""
        message = str(row.message) if pd.notna(row.message) else ""

        if not subject and not message:
            classification = "unclassified_empty"
        else:
            print(f"  Classifying Email ID: {email_id}...")
            try:
                system_prompt = ( # Using the refined prompt
                    "You are an AI assistant helping a fashion store accurately categorize customer emails. "
                    "Your goal is to determine if the email is immediately actionable as an order or if it requires further information/clarification first. "
                    "Categorize the email into ONLY one of these two types: 'product inquiry' or 'order request'.\n\n"
                    "DEFINITIONS:\n"
                    "- 'order request': The customer clearly states they want to purchase one or more **specific, identifiable products**. This means they MUST mention specific Product IDs (like 'SKU1234'), exact Product Names as they appear in a catalog (e.g., 'Classic Leather Tote Bag', not just 'a leather bag'), or unique identifying features combined with strong purchase intent (e.g., 'I want to buy the blue silk scarf shown on page 5'). Phrases like 'I want to order', 'I'd like to purchase', 'add this to my cart' are common but MUST be accompanied by specific item identification.\n\n"
                    "- 'product inquiry': The customer is asking questions OR expressing a need without specifying exact items. This includes:\n"
                    "    - Asking about availability, features, sizing, materials, restocks.\n"
                    "    - Asking for recommendations or suitability ('will this fit?', 'what bag is best for...?').\n"
                    "    - Expressing intent to buy a **type** of product but **not a specific one** (e.g., 'I need to buy summer sandals', 'I'm looking for a black wallet'). Treat these as inquiries needing product suggestions, even if they use phrases like 'I need to buy' or 'I want'.\n"
                    "    - Any request that lacks sufficient detail (specific product name/ID) to be immediately processed as an order.\n\n"
                    "Output ONLY the category name ('product inquiry' or 'order request') and nothing else."
                )
                user_prompt = f"Classify this email based on the definitions provided:\n\nSubject: {subject}\nMessage:\n{message}"

                response = client.chat.completions.create(
                    model=GPT_MODEL,
                    messages=[
                        {"role": "system", "content": system_prompt},
                        {"role": "user", "content": user_prompt}
                    ],
                    temperature=0.0,
                    max_tokens=10
                )
                classification = response.choices[0].message.content.strip().lower()
                if classification not in ["product inquiry", "order request"]:
                    print(f"    Warning: Unexpected classification '{classification}'. Marking as 'unclassified_ambiguous'.")
                    classification = "unclassified_ambiguous"
                else:
                    print(f"    -> Classified as: '{classification}'")

            except Exception as e:
                print(f"    Error classifying Email ID {email_id}: {e}")
                classification = "classification_error"

        classification_results.append({"email ID": email_id, "category": classification})

    print("--- Task 1: Email Classification Finished ---")
    email_classification_df = pd.DataFrame(classification_results)
    print("\nClassification Counts:")
    print(email_classification_df['category'].value_counts())
    return email_classification_df
