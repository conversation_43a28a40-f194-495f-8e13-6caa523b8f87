-- Sample SQL for testing purposes

SELECT
    order_id,
    customer_id,
    order_date,
    SUM(price * quantity) AS total_amount
FROM
    orders
WHERE
    order_date >= '2023-01-01'
GROUP BY
    order_id, customer_id, order_date
HAVING
    SUM(price * quantity) > 100
ORDER BY
    order_date DESC;

-- Another sample query

CREATE TABLE products (
    product_id INT PRIMARY KEY,
    product_name VARCHAR(255) NOT NULL,
    category VARCHAR(100),
    price DECIMAL(10, 2)
);

INSERT INTO products (product_id, product_name, category, price)
VALUES
    (1, 'Laptop', 'Electronics', 1200.00),
    (2, 'Mouse', 'Electronics', 25.00),
    (3, 'Keyboard', 'Electronics', 75.00);
