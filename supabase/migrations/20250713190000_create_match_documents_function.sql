CREATE OR REPLACE FUNCTION public.match_documents_filtered(
  query_embedding vector(768),
  match_threshold float,
  match_count int,
  technology_filter text DEFAULT NULL
)
RETURNS TABLE (
  content text,
  metadata jsonb,
  document_id text,
  similarity float
)
LANGUAGE plpgsql
AS $$
BEGIN
  RETURN QUERY
  SELECT
    de.content,
    de.metadata,
    de.document_id,
    1 - (de.embedding <=> query_embedding) as similarity
  FROM
    public.document_embeddings de
  WHERE
    1 - (de.embedding <=> query_embedding) > match_threshold
    -- Removed technology filter since documents don't have technology metadata
    -- AND (technology_filter IS NULL OR de.metadata->>'technology' = technology_filter)
  ORDER BY
    de.embedding <=> query_embedding
  LIMIT
    match_count;
END;
$$;
