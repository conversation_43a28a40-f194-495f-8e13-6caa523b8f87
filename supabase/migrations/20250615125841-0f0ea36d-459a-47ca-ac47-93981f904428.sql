
-- Create a public storage bucket for documents
INSERT INTO storage.buckets (id, name, public, file_size_limit, allowed_mime_types)
VALUES (
  'documents', 
  'documents', 
  true, 
  5242880, -- 5MB limit
  ARRAY['application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document']
);

-- Create a policy to allow public access to documents
CREATE POLICY "Public Access" ON storage.objects FOR SELECT USING (bucket_id = 'documents');

-- Create a policy to allow authenticated users to upload documents (for you to manage your CV)
CREATE POLICY "Authenticated users can upload documents" ON storage.objects FOR INSERT 
WITH CHECK (bucket_id = 'documents' AND auth.role() = 'authenticated');

-- Create a policy to allow authenticated users to update documents
CREATE POLICY "Authenticated users can update documents" ON storage.objects FOR UPDATE 
USING (bucket_id = 'documents' AND auth.role() = 'authenticated');

-- Create a policy to allow authenticated users to delete documents
CREATE POLICY "Authenticated users can delete documents" ON storage.objects FOR DELETE 
USING (bucket_id = 'documents' AND auth.role() = 'authenticated');
