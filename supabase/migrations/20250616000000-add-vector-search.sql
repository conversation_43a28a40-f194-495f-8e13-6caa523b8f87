-- Add the 'embedding' column if it doesn't exist
ALTER TABLE public.query_embeddings_cache
ADD COLUMN IF NOT EXISTS embedding vector(768);

-- Add the 'cached_response' column to store the full AI response
ALTER TABLE public.query_embeddings_cache
ADD COLUMN IF NOT EXISTS cached_response text;

-- Add the 'query_text' column if it doesn't exist
ALTER TABLE public.query_embeddings_cache
ADD COLUMN IF NOT EXISTS query_text text;

-- Create an HNSW index on the embedding column for fast semantic search
CREATE INDEX IF NOT EXISTS query_embeddings_cache_query_embedding_hnsw_idx
ON public.query_embeddings_cache
USING hnsw (embedding vector_cosine_ops)
WITH (m = 16, ef_construction = 64);

-- Create a function to search for semantically similar cached queries
CREATE OR REPLACE FUNCTION match_cached_queries(
  query_embedding vector(768),
  similarity_threshold float DEFAULT 0.9
)
RETURNS TABLE (
  query_hash text,
  cached_response text,
  similarity float
)
LANGUAGE sql
AS $$
  SELECT
    qec.query_hash,
    qec.cached_response,
    1 - (qec.embedding <=> query_embedding) as similarity
  FROM public.query_embeddings_cache qec
  WHERE 1 - (qec.embedding <=> query_embedding) > similarity_threshold
  ORDER BY qec.embedding <=> query_embedding
  LIMIT 1;
$$;

-- Add comments for clarity
COMMENT ON COLUMN public.query_embeddings_cache.embedding IS 'Vector embedding of the user query for semantic search.';
COMMENT ON COLUMN public.query_embeddings_cache.cached_response IS 'The full AI-generated response cached for this query.';
COMMENT ON COLUMN public.query_embeddings_cache.query_text IS 'The original text of the user query.';
COMMENT ON FUNCTION match_cached_queries IS 'Searches for semantically similar cached queries and returns their responses.';
