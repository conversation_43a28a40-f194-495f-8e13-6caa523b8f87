
-- Enable the pgvector extension for vector operations
CREATE EXTENSION IF NOT EXISTS vector;

-- Create table for storing document embeddings
CREATE TABLE public.document_embeddings (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  document_id TEXT NOT NULL UNIQUE,
  content TEXT NOT NULL,
  metadata JSONB NOT NULL DEFAULT '{}',
  embedding vector(768), 
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
);

-- Create index for faster similarity searches
CREATE INDEX ON public.document_embeddings USING ivfflat (embedding vector_cosine_ops)
WITH (lists = 10);

-- Create table for chat history and response caching
CREATE TABLE public.chat_sessions (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  session_id TEXT NOT NULL,
  user_query TEXT NOT NULL,
  ai_response TEXT NOT NULL,
  context_documents JSONB DEFAULT '[]',
  response_cached BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
);

-- Create index for faster query lookups
CREATE INDEX idx_chat_sessions_query ON public.chat_sessions USING gin (to_tsvector('english', user_query));
CREATE INDEX idx_chat_sessions_session_id ON public.chat_sessions (session_id);
CREATE INDEX idx_chat_sessions_cached ON public.chat_sessions (response_cached);

-- Enable Row Level Security (making tables public for now since this is a personal site)
ALTER TABLE public.document_embeddings ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.chat_sessions ENABLE ROW LEVEL SECURITY;

-- Create policies for public access (adjust these if you add authentication later)
CREATE POLICY "Public read access for embeddings" ON public.document_embeddings FOR SELECT USING (true);
CREATE POLICY "Public insert access for embeddings" ON public.document_embeddings FOR INSERT WITH CHECK (true);
CREATE POLICY "Public update access for embeddings" ON public.document_embeddings FOR UPDATE USING (true);

CREATE POLICY "Public read access for chat sessions" ON public.chat_sessions FOR SELECT USING (true);
CREATE POLICY "Public insert access for chat sessions" ON public.chat_sessions FOR INSERT WITH CHECK (true);
