CREATE OR REPLACE FUNCTION public.keyword_search(
  query_keywords text[],
  doc_limit int DEFAULT 5
)
RETURNS TABLE (
  content text,
  metadata jsonb,
  document_id text,
  similarity float
)
LANGUAGE plpgsql
AS $function$
BEGIN
  RETURN QUERY
  SELECT
    de.content,
    de.metadata,
    de.document_id,
    ts_rank_cd(to_tsvector('english', de.content), websearch_to_tsquery('english', array_to_string(query_keywords, ' | ')))::float AS similarity
  FROM
    public.document_embeddings de
  WHERE
    to_tsvector('english', de.content) @@ websearch_to_tsquery('english', array_to_string(query_keywords, ' | '))
  ORDER BY
    similarity DESC
  LIMIT
    doc_limit;
END;
$function$;
